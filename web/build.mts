#!/usr/bin/env npx kubik

import esbuild from 'esbuild';
import fs from 'fs';
import { glob } from 'glob';
import { Task } from 'kubik';
import path from 'path';
import url from 'url';
import packageJSON from '../package.json' assert { type: 'json' };

const { __dirname, __filename, $ } = Task.init(import.meta, {
  name: 'web',
  watch: [ './src', './tsconfig.json' ],
  ignore: [ './src/generated' ],
  deps: [
    '../shared/build.mts',
    '../report/build.mts',
    '../server/build.mts',
    '../sdk/build.mts',
    '../docs/build.mts',
  ],
});

const outDir = path.join(__dirname, 'dist');
const srcDir = path.join(__dirname, 'src');
const generatedSrc = path.join(srcDir, 'generated');

await fs.promises.rm(outDir, { recursive: true, force: true });

await fs.promises.rm(generatedSrc, { recursive: true, force: true });
await fs.promises.mkdir(generatedSrc, { recursive: true }).catch(e => {});

// Find all shoelace used components.
const typescriptFiles = await glob('src/**/*.ts', { absolute: true });
const shoelaceTagNames = new Set();
const filesWithCustomElementDefinitions = new Set<string>();
await Promise.all(typescriptFiles.map(async tsFile => {
  const contents = await fs.promises.readFile(tsFile, 'utf-8');
  if (contents.includes('@customElement'))
    filesWithCustomElementDefinitions.add(tsFile);
  for (const [match, group] of contents.matchAll(/<sl-([\w-]+)/g))
    shoelaceTagNames.add(group);
}));

// Cherry-pick only used components from shoelace to keep the bundle size small.
await fs.promises.writeFile(path.join(generatedSrc, 'shoelaceImports.ts'), [
  `// GENERATED BY ${path.basename(__filename)}`,
  ``,
  ...[...shoelaceTagNames].sort().map(tagName => `import '@shoelace-style/shoelace/dist/components/${tagName}/${tagName}.js';`)
].join('\n'));

// Auto-import all custom element definitions.
await fs.promises.writeFile(path.join(generatedSrc, 'componentImports.ts'), [
  `// GENERATED BY ${path.basename(__filename)}`,
  ``,
  ...[...filesWithCustomElementDefinitions].sort().map(componentFile => `import '${path.relative(generatedSrc, componentFile).replace(/\.ts$/, '.js')}';`)
].join('\n'));

const { stdout: COMMIT_SHA } = await $({ stdio: 'pipe' })`git rev-parse HEAD`;

const result1 = await esbuild.build({
  color: true,
  entryPoints: [path.join(srcDir, 'index.ts')],
  define: {
    COMMIT_SHA: JSON.stringify(COMMIT_SHA),
    BUILD_TIMESTAMP: JSON.stringify(Date.now()),
    PACKAGE_JSON_VERSION: JSON.stringify(packageJSON.version),
  },
  outdir: outDir,
  format: 'esm',
  bundle: true,
  sourcemap: true,
  minify: false,
  metafile: true,
});


fs.writeFileSync('meta.json', JSON.stringify(result1.metafile))

if (result1.errors.length)
  process.exit(1);

const result2 = await esbuild.build({
  color: true,
  entryPoints: [path.join(srcDir, 'style.css')],
  outdir: outDir,
  format: 'esm',
  bundle: true,
  sourcemap: true,
  minify: false,
  loader: {
    '.woff2': 'copy',
  },
});

if (result2.errors.length)
  process.exit(1);

// Copy all image files
const assets = await glob('src/**/*.{html,png,jpg,jpeg,svg}', { absolute: true });
for (const srcPath of assets) {
  const outPath = path.join(outDir, path.relative(srcDir, srcPath));
  await fs.promises.mkdir(path.dirname(outPath), { recursive: true }).catch(e => {});
  await fs.promises.copyFile(srcPath, outPath);
}

// Copy all shoelace assets
const shoelacePath = url.fileURLToPath(import.meta.resolve('@shoelace-style/shoelace/dist/shoelace.js'));
await fs.promises.cp(path.join(path.dirname(shoelacePath), 'assets'), path.join(outDir, 'assets'), { recursive: true });

// Copy all boxicon svgs
const boxiconsPath = url.fileURLToPath(import.meta.resolve('boxicons'));
await fs.promises.cp(path.join(path.dirname(boxiconsPath), '..', 'svg'), path.join(outDir, 'assets', 'boxicons'), { recursive: true });

// Copy documentation
await fs.promises.cp(path.join(__dirname, '..', 'docs', 'dist'), path.join(outDir, 'docs'), { recursive: true });

await $`tsc --pretty -p .`;
