import { FlakinessReport } from '@flakiness/report';
import { PageOptions } from '@flakiness/server/common/pagedResponse.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { timeDay, timeFormat, timeHour, timeMillisecond } from 'd3';
import { ReactiveController, ReactiveControllerHost } from 'lit';
import scrollIntoView from 'scroll-into-view-if-needed';

export const HISTORY_SNIPPET_DAYS = 14;

const listenersSymbol = Symbol('listeners');

export type Disposable = () => void;
export type EventListener<T extends any[]> = (...args: T) => void;
export type SimpleEvent<T extends any[]> = Function & {
  [listenersSymbol]: Set<EventListener<T>>,
};

const fmtMonthAndDay = timeFormat('%B %d');
const fmtYear = timeFormat('%Y');

export function isWebUrl(string: string): boolean {
  try {
    const url = new URL(string);
    // 1. Protocol must be http: or https:
    // 2. Hostname must exist (prevents "http:foo" or "https:/" errors)
    // 3. Hostname must contain a dot (prevents localhost or single-word inputs like "http://test")
    return (url.protocol === 'http:' || url.protocol === 'https:') &&
           url.hostname.includes('.');
  } catch (err) {
    return false;
  }
}

function renderDate(date: Date) {
  const today = new Date();

  // If both since and until dates from this year, then render no YEAR.
  if (date.getFullYear() === today.getFullYear())
    return `${fmtMonthAndDay(date)}`;
  return `${fmtMonthAndDay(date)}, ${fmtYear(date)}`;
}

export function renderDatesRange(since: Date, until: Date) {
  until = timeMillisecond.offset(until, -1);
  if (+timeDay.floor(since) === +timeDay.floor(until))
    return renderDate(since);

  const today = new Date();

  // If both since and until dates from this year, then render no YEAR.
  if (since.getFullYear() === today.getFullYear() && until.getFullYear() === today.getFullYear())
    return `${fmtMonthAndDay(since)}—${fmtMonthAndDay(until)}`;
  // Otherwise, render 1 year if the d  ates are from the same year
  if (since.getFullYear() === until.getFullYear())
    return `${fmtMonthAndDay(since)} — ${fmtMonthAndDay(until)}, ${fmtYear(since)}`;
  // If dates span multiple years, then render both years
  return `${fmtMonthAndDay(since)}, ${fmtYear(since)} — ${fmtMonthAndDay(until)}, ${fmtYear(until)}`;
}

export function commitLastXDays(commit: WireTypes.Commit, n: number) {
  return lastXDays(new Date(commit.timestamp), n);
}

export function lastXDays(date: Date, n: number) {
  return {
    since: timeDay.floor(timeDay.offset(date, -n + 1)),
    until: timeDay.ceil(date),
  };
}

export function commitLast7Days(commit: WireTypes.Commit) {
  return commitLastXDays(commit, 7);
}

export function wireDays(options: {
  since: Date, 
  until: Date,
  backfillDays: number,
}): WireTypes.Day[] {
  const fromDate = timeDay.floor(options.since);
  const toDate = timeDay.ceil(options.until);
  return timeDay.range(timeDay.offset(fromDate, -options.backfillDays), toDate).map(d => ({
    sinceTimestamp: +d as FlakinessReport.UnixTimestampMS,
    untilTimestamp: +timeDay.offset(d, 1) as FlakinessReport.UnixTimestampMS,
  })).reverse();
}

export function commitWeek(commit: WireTypes.Commit) {
  const commitDate = new Date(commit.timestamp);
  const dayOfWeek = commitDate.getDay();
  return {
    since: timeDay.floor(timeDay.offset(commitDate, -dayOfWeek)),
    until: timeDay.ceil(timeDay.offset(commitDate, 6 - dayOfWeek)),
  };
}

export function midday(date: Date) {
  return timeHour.offset(timeDay.floor(date), 12);
}


export function commitPreviousWeek(commit: WireTypes.Commit) {
  const commitDate = new Date(commit.timestamp);
  const dayOfWeek = commitDate.getDay();
  return {
    since: timeDay.floor(timeDay.offset(commitDate, -dayOfWeek - 7)),
    until: timeDay.ceil(timeDay.offset(commitDate, -dayOfWeek - 1)),
  };
}

export async function loadAllPages<E>(loadPage: (page: PageOptions) => Promise<WireTypes.PagedResponse<E>>): Promise<E[]> {
  let pageNumber = 0;
  const pageSize = 20;
  const firstPage = await loadPage({ number: pageNumber, size: pageSize });
  const pages: WireTypes.PagedResponse<E>[] = [];
  pages.push(firstPage);
  for (let page = 1; page <= firstPage.totalPages; ++page)
    pages.push(await loadPage({ number: page, size: pageSize }));
  return pages.map(page => page.elements).flat();
}

export function capitalize(word: string) {
  return word[0].toUpperCase() + word.substring(1);
}

export function onDOMEvent<E extends keyof DocumentEventMap>(target: Document, event: E, handler: (this: Document, ev: DocumentEventMap[E]) => any, capturing?: boolean): Disposable;
export function onDOMEvent<E extends keyof ElementEventMap>(target: Element, event: E, handler: (this: Element, ev: ElementEventMap[E]) => any, capturing?: boolean): Disposable;
export function onDOMEvent<E extends keyof WindowEventMap>(target: Window, event: E, handler: (this: Window, ev: WindowEventMap[E]) => any, capturing?: boolean): Disposable;
export function onDOMEvent<E extends keyof HTMLElementEventMap>(target: HTMLElement, event: E, handler: (this: HTMLElement, ev: HTMLElementEventMap[E]) => any, capturing?: boolean): Disposable;

export function onDOMEvent(target: Document|Window|Element|HTMLElement,
    event: keyof ElementEventMap | keyof WindowEventMap | keyof DocumentEventMap | keyof HTMLElementEventMap,
    handler: (this: HTMLElement | Element | Window | Document, ev: Event) => any, capturing = false): Disposable {
  target.addEventListener(event, handler, capturing);
  return () => target.removeEventListener(event, handler, capturing);
}

export function assert(value: unknown, message?: string): asserts value {
  if (!value)
    throw new Error(`Assertion failed ${message ?? ''}`.trim())
}

export function onCustomDOMEvent(target: Document|Window|Element,
    event: string,
    handler: (this: Element | Window | Document, ev: Event) => any, capturing = false): Disposable {
  target.addEventListener(event, handler, capturing);
  return () => target.removeEventListener(event, handler, capturing);
}

export function createEvent<T extends any[]>(): SimpleEvent<T> {
  const listeners = new Set<EventListener<T>>();
  const subscribeFunction: {
    (listener: EventListener<T>): Disposable;  // Main function type
    [listenersSymbol]: Set<EventListener<T>>;  // Extra property on the function
  } = listener => {
    listeners.add(listener);
    return () => listeners.delete(listener);
  }
  subscribeFunction[listenersSymbol] = listeners;
  return subscribeFunction;
}

export function emitEvent<T extends any[]>(event: SimpleEvent<T>, ...args: T) {
  /* @type {Set<function>} */
  let listeners = event[listenersSymbol];
  if (!listeners || !listeners.size)
    return;
  listeners = new Set(listeners);
  for (const listener of listeners)
    listener.call(null, ...args);
}

export function disposeAll(disposables: Disposable[]) {
  for (const d of disposables)
    d.call(null);
  disposables.splice(0);
}

export function consumeDOMEvent(event: Event) {
  event.stopPropagation();
  event.preventDefault();
}

export function preventTextSelectionOnDBLClick(element: HTMLElement) {
  // Prevent text selection on dblclick.
  element.addEventListener('mousedown', (event: MouseEvent) => {
    if (event.detail > 1)
      consumeDOMEvent(event);
  }, true);
}

export function scrollIntoViewIfNeeded(element: HTMLElement) {
  scrollIntoView(element, {
    block: 'center',
    behavior: 'instant',
    scrollMode: 'if-needed',
  });
}

/**
 * Serializing async operations one-by-one.
 */
export class CriticalSection {
  private _rollingPromises: Map<any, any>;
  constructor() {
    this._rollingPromises = new Map();
  }

  async run(key: string, operation: Function) {
    const rollingPromise = this._rollingPromises.get(key) || Promise.resolve();
    const resultPromise = rollingPromise.then(() => operation());
    const newRollingPromise = resultPromise.finally(() => {
      if (this._rollingPromises.get(key) === newRollingPromise)
        this._rollingPromises.delete(key);
    }).catch((e: any) => {/* swallow error */});
    this._rollingPromises.set(key, newRollingPromise);
    return resultPromise;
  }
}

export function deepQuerySelectorAll(selector: string, root: Document | DocumentFragment | HTMLElement | ShadowRoot = document, result: Element[] = []): Element[] {
  result.push(...root.querySelectorAll(selector));
  for (const element of root.querySelectorAll('*')) {
    if (element.shadowRoot)
      deepQuerySelectorAll(selector, element.shadowRoot, result);
  }
  return result;
}

export function deepQuerySelector(selector: string, root?: Document | DocumentFragment | HTMLElement | ShadowRoot) {
  const elements = deepQuerySelectorAll(selector, root);
  return elements.length ? elements[0] : null;
}

export class Throttler {
  private _pendingOperation: Function | null = null;
  private _runningOperation: Promise<any> | null = null;

  constructor(private _timeout = 0) {
    this._pendingOperation = null;
    this._runningOperation = null;
  }

  schedule(operation: Function) {
    this._pendingOperation = operation;
    this._maybeRun();
  }

  reset() {
    this._pendingOperation = null;
  }

  async complete() {
    await this._pendingOperation;
  }

  _maybeRun() {
    if (this._runningOperation || !this._pendingOperation)
      return;
    const operation = this._pendingOperation;
    this._pendingOperation = null;
    this._runningOperation = Promise.resolve()
        .then(() => operation.call(null))
        .catch(e => console.error(e))
        .then(() => this._timeout ? new Promise(x => setTimeout(x, this._timeout)) : undefined)
        .then(() => {
          this._runningOperation = null;
          this._maybeRun();
        });
  }
}

export class EventManager<T> implements ReactiveController {
  private _scope?: T;
  private _eventListeners: Disposable[] = [];

  constructor(host: ReactiveControllerHost, private _getScope: () => (T|undefined), private _doSubscribe: (scope: T) => Disposable[]) {
    host.addController(this);
  }

  hostDisconnected(): void {
    this._scope = undefined;
    disposeAll(this._eventListeners);
  }

  private _ensureSubscribed() {
    const scope = this._getScope();
    if (scope !== this._scope)
      disposeAll(this._eventListeners);
    this._scope = scope;
    if (this._scope)
      this._eventListeners = this._doSubscribe(this._scope);
  }

  hostConnected(): void {
    this._ensureSubscribed();
  }

  hostUpdate(): void {
    this._ensureSubscribed();
  }
}

// Helper type to transform all elements of a tuple to their non-nullable versions
type NonNullableElements<T> = {
  [K in keyof T]: NonNullable<T[K]>
};

export function iff<T extends any[], K>(
  ...args: [...T, (...args: NonNullableElements<T>) => K]
): K | undefined;
export function iff<T extends any[], K>(
  ...args: [...T, (...args: NonNullableElements<T>) => K, () => K]
): K | undefined;
export function iff<T extends any[], K>(...args: [...T, (...args: NonNullableElements<T>) => K, (() => K)?]): K|undefined {
  // Extract the function from the arguments
  let thenFn = args[args.length - 1] as (...args: NonNullableElements<T>) => K;
  let elseFn = undefined;
  
  if (args[args.length - 2] instanceof Function) {
    elseFn = thenFn as () => K;
    thenFn = args[args.length - 2] as (...args: NonNullableElements<T>) => K;
  }

  let callargs = elseFn ? args.slice(0, -2) : args.slice(0, -1);

  // Check if all arguments except the last (the function) are defined
  if (callargs.every(arg => arg !== undefined)) {
      // Call the function with the arguments, casting them to non-nullable types
      return thenFn(...(args.slice(0, -1) as NonNullableElements<T>));
  }
  if (elseFn)
    return elseFn();
  return undefined;
}

export function getInitials(userName: string) {
  const [name = '', surname = ''] = userName.split(/\s+/);
  let initials = '';
  if (name.length)
    initials += name[0];
  if (surname.length)
    initials += surname[0];
  return initials;
}

export function humanReadableMs(ms: number | { valueOf(): number }): string {
  if (+ms < 1e-5)
    return `0 ms`;
  if (+ms < 1)
    return `${Math.round(+ms * 1000)} µs`
  ms = Math.round(+ms);
  let seconds = ((+ms) / 1000);
  if (seconds < 1)
    return `${ms} ms`;
  if (seconds < 60)
    return `${seconds.toFixed(1)} sec`;
  seconds = Math.round(seconds);
  let minutes = (seconds / 60)|0;
  seconds = seconds % 60;
  if (minutes < 1)
    return `${seconds} sec`;
  let hours = (minutes / 60)|0;
  minutes = minutes % 60;
  if (hours < 1)
    return seconds !== 0 ? `${minutes} min ${seconds} sec` : `${minutes} min`;
  let days = (hours / 24)|0;
  if (days < 1)
    return `${hours} h ${minutes} min`;
  hours = hours % 24;
  let weeks = (days / 7)|0;
  if (weeks < 1)
    return `${days} days ${hours} h ${minutes} min`;
  days %= 7;
  return `${weeks} weeks ${days} days ${hours} h`;
}

export function humanReadableBytes(bytes: number): string {
  const units = ["B", "KB", "MB", "GB", "TB", "PB"];
  let i = 0;
  while (bytes >= 1000 && i < units.length - 1) {
    bytes /= 1000;
    i++;
  }
  // For small integer values, we care about float part.
  // For others, we only care about integer part.
  let rounded = bytes.toFixed(1);
  if (Math.round(bytes) > 99 || bytes === 0)
    rounded = Math.round(bytes) + '';
  return `${rounded} ${units[i]}`;
}

export function humanReadableInteger(value: number): string {
  const units = ["", "K", "M", "B", "T"];
  let i = 0;
  while (value >= 1000 && i < units.length - 1) {
    value /= 1000;
    i++;
  }
  return `${Math.floor(value)}${units[i]}`;
}
