import { FlakinessReport } from '@flakiness/report';
import { ReportUtils } from '@flakiness/report';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { SlPopup } from '@shoelace-style/shoelace';
import * as d3 from 'd3';
import { css, html, LitElement, nothing, PropertyValues } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import { styleMap } from 'lit/directives/style-map.js';
import ms from 'ms';
import { assert, humanReadableBytes } from '../utils.js';
import { linkStyles } from './cssstyles.js';

type TestRun = {
  since: number,
  until: number,
  outcome: WireTypes.Outcome,
  test: FlakinessReport.Test,
  attempt: FlakinessReport.RunAttempt,
}

type Sample = {
  timestamp: number,
  value: number,
}

@customElement('chart-run-workers')
export class ChartRunWorkers extends LitElement {
  @property({ attribute: false }) report?: FlakinessReport.Report;
  @property({ attribute: false }) testRun?: FlakinessReport.RunAttempt;

  @query("svg") private _svg?: SVGSVGElement;
  @query('section') private _grid?: HTMLElement;

  @query("sl-popup") private _popup?: SlPopup;

  @state() private _testRuns: TestRun[][] = [];
  @state() private _cpuUsage: Sample[] = [];
  @state() private _memUsage: Sample[] = [];

  @state() private _mouse?: { x: number, y: number, clientX: number, cpuPoint?: DOMPoint, memPoint?: DOMPoint };

  private _yCpuScale?: d3.ScaleLinear<number, number>;
  private _yMemScale?: d3.ScaleLinear<number, number>;

  protected willUpdate(_changedProperties: PropertyValues<this>): void {
    if (_changedProperties.has('report')) {
      this._testRuns = this._computeTestRuns();
      this._cpuUsage = this._computeCpuUsage();
      this._memUsage = this._computeMemUsage();
    }
  }

  private _computeTestRuns() {
    const report = this.report;
    if (!report)
      return [];
    const testRuns: TestRun[][] = [];
    ReportUtils.visitTests(report, (test, parents) => {
      for (let attemptIdx = 0; attemptIdx < test.attempts.length; ++attemptIdx) {
        const attempt = test.attempts[attemptIdx];
        if (attempt.parallelIndex === undefined)
          continue;
        if (!testRuns[attempt.parallelIndex])
          testRuns[attempt.parallelIndex] = [];
        let outcome: WireTypes.Outcome;
        if (attempt.status === attempt.expectedStatus)
          outcome = 'expected';
        else
          outcome = 'unexpected';
        testRuns[attempt.parallelIndex].push({
          test,
          attempt,
          since: attempt.startTimestamp - report.startTimestamp,
          until: attempt.duration + attempt.startTimestamp - report.startTimestamp,
          outcome,
        });
      }
    });
    return testRuns;
  }

  private _computeCpuUsage() {
    const report = this.report;
    if (!report)
      return [];
    const cpuUsage: Sample[] = [];
    let ts = report.systemUtilization?.startTimestamp ?? 0;
    for (const s of report.systemUtilization?.samples ?? []) {
      ts += s.dts;
      cpuUsage.push({ timestamp: ts - report.startTimestamp, value: s.cpuUtilization });
    }
    if (cpuUsage.length) {
      cpuUsage.unshift({ timestamp: 0, value: 0 });
      cpuUsage.push({ timestamp: report.duration, value: 0 });
    }
    return cpuUsage
  }

  private _computeMemUsage() {
    const report = this.report;
    if (!report)
      return [];
    const memUsage: Sample[] = [];
    let ts = report.systemUtilization?.startTimestamp ?? 0;
    for (const s of report.systemUtilization?.samples ?? []) {
      ts += s.dts;
      memUsage.push({ timestamp: ts - report.startTimestamp, value: s.memoryUtilization });
    }
    if (memUsage.length) {
      memUsage.unshift({ timestamp: 0, value: 0 });
      memUsage.push({ timestamp: report.duration, value: 0 });
    }
    return memUsage;
  }

  protected override firstUpdated(_changedProperties: PropertyValues): void {
    if (!this._popup || !this._svg)
      return;
    this._popup.anchor = {
      getBoundingClientRect: () => {
        const box = this._svg?.getBoundingClientRect();
        return {
          width: 0,
          height: 0,
          x: this._mouse?.clientX ?? 0,
          y: box?.y ?? 0,
          top: box?.y ?? 0,
          right: this._mouse?.clientX ?? 0,
          bottom: box?.y ?? 0,
          left: this._mouse?.clientX ?? 0,
        } as DOMRect;
      }
    };
  }

  protected updated(changedProperties: PropertyValues): void {
    if (changedProperties.size === 1 && changedProperties.has('_mouse')) {
      this._updateChart(false);
    } else {
      this._updateChart(true);
    }
  }

  private _onMouseMove(event: MouseEvent) {
    const [mx, my] = d3.pointer(event);

    const cpuPath = this._svg?.querySelector<SVGPathElement>('#cpu-mask path')!;
    const cpuPoint = this._cpuUsage.length ? searchPointOnPath(cpuPath, mx) : undefined;
    const memPath = this._svg?.querySelector<SVGPathElement>('#mem-mask path')!;
    const memPoint = this._memUsage.length ? searchPointOnPath(memPath, mx) : undefined;

    this._mouse = {
      x: mx,
      y: my,
      clientX: event.clientX,
      cpuPoint,
      memPoint,
    };
    this._popup!.active = !!memPoint || !!cpuPoint;
    this._popup?.reposition();
  }

  private _onClick(event: MouseEvent) {
    const element = this.renderRoot.querySelector('rect:hover');
    const datum = element ? d3.select(element).datum() as TestRun : undefined;
    if (!datum || !this.report)
      return;
    this._mouse = undefined;
    this._popup!.active = false;
    this.dispatchEvent(new CustomEvent<{ report: FlakinessReport.Report, test: FlakinessReport.Test, attempt: FlakinessReport.RunAttempt}>('fk-testrun-selected', {
      detail: datum.attempt === this.testRun ? undefined : {
        report: this.report,
        test: datum.test,
        attempt: datum.attempt,
      }
    }));
  }

  private _onMouseLeave() {
    this._mouse = undefined;
    this._popup!.active = false;
  }

  private _updateChart(full: boolean) {
    const { width: WIDTH, height: HEIGHT } = this._svg?.getBoundingClientRect() ?? {};
    if (!this._grid || !this._svg || !this.report || !WIDTH || !HEIGHT)
      return
    
    // Measure label boxes relative to grid container.
    const boxCpuLabel = getRelativeRect(this._grid.querySelector('.label.cpu'));
    d3.select(this._svg)
      .select('.cpu-usage')
      .attr('transform', `translate(0, ${boxCpuLabel.y1})`);

    const boxMemLabel = getRelativeRect(this._grid.querySelector('.label.mem'));
    d3.select(this._svg)
      .select('.mem-usage')
      .attr('transform', `translate(0, ${boxMemLabel.y1})`);

    const boxWorkerLabels = [...this._grid.querySelectorAll<HTMLElement>('.label.worker')].map(getRelativeRect);

    const boxAxisLabel = getRelativeRect(this._grid.querySelector('.label.xaxis'));
    const xaxisElement = d3.select(this._svg)
      .select<SVGGElement>('g.xaxis')
      .attr('transform', `translate(0, ${boxAxisLabel.y1})`);

    const data = this._testRuns;

    const allIntervals = data.flat();
    const domainStart = 0;
    const domainEnd = Math.max((d3.max(allIntervals, d => d.until) ?? 0), this.report.duration);
    const selectedInterval = this.testRun ? allIntervals.find(interval => interval.attempt === this.testRun) : undefined;
    const padding = selectedInterval ? (selectedInterval.until - selectedInterval.since) : 0;
    const minTime = selectedInterval ? Math.max(selectedInterval.since - padding, domainStart) : domainStart;
    const maxTime = selectedInterval ? Math.min(selectedInterval.until + padding, domainEnd) : domainEnd;
    const x = d3.scaleLinear()
      .domain([minTime, maxTime])
      .range([boxAxisLabel.width, WIDTH]);
    const yCpuScale = d3.scaleLinear()
      .domain([0, 100])
      .range([boxCpuLabel.height, 0]);
    this._yCpuScale = yCpuScale;
    const yMemScale = d3.scaleLinear()
      .domain([0, 100])
      .range([boxMemLabel.height, 0]);
    this._yMemScale = yMemScale;

    const xAxis = d3.axisBottom<number>(x)
      .tickFormat(xAxisTickFormat(maxTime - minTime));

    const cpuHover = d3.select(this._svg).select('#cpu-mask circle');
    
    if (this._mouse?.cpuPoint) {
      cpuHover
        .attr('cx', this._mouse.x)
        .attr('cy', this._mouse.cpuPoint.y)
        .attr('r', 3)
        .attr('display', null)
    } else {
      cpuHover.attr('display', 'none');
    }

    const memHover = d3.select(this._svg).select('#mem-mask circle');
    if (this._mouse?.memPoint) {
      memHover
        .attr('cx', this._mouse.x)
        .attr('cy', this._mouse.memPoint.y)
        .attr('r', 3)
        .attr('display', null)
    } else {
      memHover.attr('display', 'none');
    }

    const hoverline = d3.select(this._svg).select('.hoverline');
    if (this._mouse) {
      hoverline
        .attr('y1', 0)
        .attr('y2', boxAxisLabel.y1)
        .attr('x1', this._mouse.x)
        .attr('x2', this._mouse.x)
        .attr('display', null)
      xAxis.tickValues([x.invert(this._mouse.x)]);
      xaxisElement.call(xAxis);
    } else {
      hoverline.attr('display', 'none');
      xaxisElement
        .transition()
        .call(xAxis);
    }
    if (!full)
      return;

    // Update CPU chart
    d3.select(this._svg)
      .selectAll('.cpu-usage rect, #cpu-mask')
      .attr('x', 0)
      .attr('y', 0)
      .attr('width', WIDTH)
      .attr('height', boxCpuLabel.height);
    const cpuTimeseries = d3.line<Sample>()
      .x(d => x(d.timestamp))
      .y(d => yCpuScale(d.value))
      .curve(d3.curveMonotoneX);
    d3.select(this._svg)
      .select('#cpu-mask path')
      .transition()
      .attr('d', cpuTimeseries(this._cpuUsage));

    // Update Mem chart
    d3.select(this._svg)
      .selectAll('.mem-usage rect, #mem-mask')
      .attr('x', 0)
      .attr('y', 0)
      .attr('width', WIDTH)
      .attr('height', boxMemLabel.height);
    const memTimeseries = d3.line<Sample>()
      .x(d => x(d.timestamp))
      .y(d => yMemScale(d.value))
      .curve(d3.curveMonotoneX);
    d3.select(this._svg)
      .select('#mem-mask path')
      .transition()
      .attr('d', memTimeseries(this._memUsage));

    // Update testruns
    d3.select(this._svg)
      .select('g.testruns')
      .selectAll('g')
      .data(data)
      .join('g')
      .each(function(processData, processIndex) {
        const { y1, height } = boxWorkerLabels[processIndex];
        d3.select(this)
          .attr('transform', `translate(0, ${y1})`)
          .selectAll('rect')
          .data(processData)
          .join(
            function(enter) {
              return enter
                .append('rect')
                .attr('class', (d: TestRun) => d.outcome)
                .classed('selected', (d: TestRun) => !selectedInterval || selectedInterval === d)
                .classed('dimmed', (d: TestRun) => !!selectedInterval && selectedInterval !== d)
                .attr("x", (d: TestRun) => x(d.since))
                .attr("y", 0)
                .attr("width", (d: TestRun) => x(d.until) - x(d.since))
                .attr("height", height)
            },
            function(update) {
              return update
                .classed('selected', (d: TestRun) => !selectedInterval || selectedInterval === d)
                .classed('dimmed', (d: TestRun) => !!selectedInterval && selectedInterval !== d)
                .transition()
                .attr("x", (d: TestRun) => x(d.since))
                .attr("width", (d: TestRun) => x(d.until) - x(d.since))
            },
            function(exit) {
              return exit.remove();
            }
          )
      });
  }

  override render() {
    if (!this._testRuns.length)
      return nothing;
    const cpuUtilization = this._yCpuScale && this._mouse?.cpuPoint ? this._yCpuScale.invert(this._mouse.cpuPoint.y) : undefined;
    const totalMem = this.report?.systemUtilization?.totalMemoryBytes ?? 0;
    const memUtilization = this._yMemScale && this._mouse?.memPoint ? this._yMemScale.invert(this._mouse.memPoint.y) : undefined;
    return html`
      <sl-popup placement="top" distance=8 arrow arrow-placement="anchor">
        <div style=${styleMap({
          background: 'white',
        })}>
          <h-box>
            ${cpuUtilization ? html`
              <h-box style="gap: var(--sl-spacing-2x-small);">
                <sl-icon name=cpu></sl-icon>
                <span>CPU</span>
                <strong>${cpuUtilization.toFixed(2)}%</strong>
              </h-box>
            ` : nothing}
            ${memUtilization ? html`
              <h-box style="gap: var(--sl-spacing-2x-small);">
                <sl-icon name=memory></sl-icon>
                <span>Memory</span>
                <strong>${humanReadableBytes(totalMem * memUtilization / 100)}</strong>
                <span>(${memUtilization.toFixed(2)}%)</span>
              </h-box>
            ` : nothing}
          </h-box>
        </div>
      </sl-popup>

      <section style="--rows: ${this._testRuns.length + 3}">
        <h-box class="label cpu" style=${styleMap({
          'grid-row': 1,
          'grid-column': 1,
          'gap': 'var(--sl-spacing-2x-small)',
          display: this._cpuUsage.length ? undefined : 'none',
        })}><sl-icon name=cpu></sl-icon>CPU</h-box>
        <h-box class="label mem" style=${styleMap({
          'grid-row': 2,
          'grid-column': 1,
          'gap': 'var(--sl-spacing-2x-small)',
          display: this._memUsage.length ? undefined : 'none',
        })}><sl-icon name=memory></sl-icon>Memory</h-box>

        ${this._testRuns.map((intervals, parallelIndex) => html`
          <h-box class="label worker" style=${styleMap({
            'grid-row': parallelIndex + 3,
            'grid-column': 1,
          })}>worker #${parallelIndex + 1}</h-box>
        `)}
        <div class="label xaxis" style=${styleMap({
          height: '16px', // this specifies the height of the xAxis. 20px should be enough.
          'grid-column': 1,
          'grid-row': this._testRuns.length + 3,
        })}></div>

        <span style=${styleMap({
          'grid-row': '1 / -1',
          'grid-column': '1 / -1',
          position: 'relative',
        })}>
          <sl-resize-observer @sl-resize=${() => this._updateChart(true) }>
            <svg id=chart
              @mousemove=${this._onMouseMove}
              @mouseleave=${this._onMouseLeave}
              @click=${this._onClick}
            >
              <defs>
                <linearGradient id=cpu-gradient x1="0" x2="0" y1="1" y2="0">
                  <stop offset="0%" stop-color="var(--color-cpu-low)"></stop>
                  <stop offset="100%" stop-color="var(--color-cpu-high)"></stop>
                </linearGradient>
                <linearGradient id=mem-gradient x1="0" x2="0" y1="1" y2="0">
                  <stop offset="0%" stop-color="var(--color-mem-low)"></stop>
                  <stop offset="100%" stop-color="var(--color-mem-high)"></stop>
                </linearGradient>
                <mask id=cpu-mask>
                  <path></path>
                  <circle></circle>
                </mask>
                <mask id=mem-mask>
                  <path></path>
                  <circle></circle>
                </mask>
              </defs>

              <g class=container>
                <g class=cpu-usage><rect></rect></g>
                <g class=mem-usage><rect></rect></g>
                <g class=testruns></g>
                <g class=xaxis></g>
              <g>

              <line class=hoverline></line>
            </svg>
          </sl-resize-observer>
        </span>

        <span class=background></span>
        <span class=glasspane></span>
      </section>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      flex-direction: column;
      flex: auto;
      --color-cpu-low: #FFCC80;
      --color-cpu-high: #F44336;
      --color-mem-low: #4FC3F7;
      --color-mem-high: #9C27B0;
    }

    section {
      display: grid;
      row-gap: 4px;
      grid-template-columns: max-content auto;
      grid-template-rows: repeat(var(--rows), auto);

      .label {
        color: var(--sl-color-neutral-500);
        justify-content: end;
        font-size: var(--sl-font-size-small);
        z-index: 10;
        padding-right: 4px;

        &.cpu, &.mem {
          font-size: var(--sl-font-size-x-small);
        }
      }

      .background {
        grid-row: 1/-2;
        grid-column: 1;
        z-index: 1;
        width: 100%;
        height: 100%;
        background: white;
      }

      .glasspane {
        grid-row: 1/-1;
        grid-column: 1;
        z-index: 1;
        width: 100%;
        height: 100%;
      }
    }

    sl-popup {
      --arrow-color: var(--sl-color-neutral-300);

      div {
        font-size: var(--sl-font-size-small);
        border: 1px solid var(--sl-color-neutral-300);
        padding: var(--sl-spacing-medium) var(--sl-spacing-medium);
        border-radius: var(--sl-border-radius-medium);
      }
    }

    svg {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      user-select: none;

      .hoverline {
        pointer-events: none;
        stroke: var(--sl-color-neutral-300);
      }

      .cpu-usage rect {
        stroke: none;
        fill: url(#cpu-gradient);
        mask: url(#cpu-mask);
      }

      .mem-usage rect {
        stroke: none;
        fill: url(#mem-gradient);
        mask: url(#mem-mask);
      }

      mask path {
        fill: #ffffff78;
        stroke: white;
        stroke-width: 1px;
      }

      mask circle {
        fill: white;
      }

      .xaxis {
        color: var(--sl-color-neutral-400);
      }

      .testruns rect {
        rx: 2px;
        ry: 2px;
        transition: filter 0.1s ease-in;
        
        &.expected {
          fill: color-mix(in srgb, var(--fk-color-outcome-expected) 50%, white 50%);
        }

        &.unexpected {
          fill: color-mix(in srgb, var(--fk-color-outcome-unexpected) 50%, white 50%);
        }

        &.selected {
        }
        &.dimmed {
          opacity: 0.5;
        }

        &:hover {
          cursor: pointer;
          filter: brightness(0.8);
          opacity: 1;
        }
      }
    }
  `];
}

function xAxisTickFormat(scale: number) {
  if (scale < ms('1 min'))
    return formatWithPrecision.bind(null, 'ms');
  if (scale < ms('1 hour'))
    return formatWithPrecision.bind(null, 'seconds');
  return formatWithPrecision.bind(null, 'minutes');
}

function formatWithPrecision(precision: 'ms'|'seconds'|'minutes'|'hours', timestamp: number) {
  timestamp = Math.round(timestamp);
  if (timestamp < 0)
    timestamp = 0;
  const ms = timestamp % 1000;
  timestamp = Math.floor(timestamp / 1000);
  const seconds = timestamp % 60;
  timestamp = Math.floor(timestamp / 60);
  const minutes = timestamp % 60;
  const hours = Math.floor(timestamp / 60);

  let result = '';
  if (hours !== 0)
    result += hours + 'h ';
  if (precision === 'hours')
    return result.trim();
  if (minutes !== 0 || precision === 'minutes')
    result += minutes + 'min ';
  if (precision === 'minutes')
    return result.trim();
  if (precision === 'seconds')
    result += seconds + 's';
  else
    result += seconds + '.' + (ms + '').padStart(3, '0') + 's';
  return result.trim();
}

function getRelativeRect(element: HTMLElement|null) {
  assert(element);
  const parentRect = element.parentElement?.getBoundingClientRect();
  assert(parentRect);
  const childRect = element.getBoundingClientRect();
  const x1 = childRect.left - parentRect.left;
  const y1 = childRect.top - parentRect.top;
  return {
    x1,
    y1,
    x2: x1 + childRect.width,
    y2: y1 + childRect.height,
    width: childRect.width,
    height: childRect.height,
  };
}

function searchPointOnPath(path: SVGPathElement, targetX: number): DOMPoint|undefined {
  const totalLength = path.getTotalLength();
  if (totalLength === 0)
    return undefined;

  // Binary search along the path to find the point closest to mouseX
  let start = 0;
  let end = totalLength;
  let point;

  while (start <= end) {
    const mid = (start + end) / 2;
    const p = path.getPointAtLength(mid);

    if (Math.abs(p.x - targetX) < 0.5) {
      point = p;
      break;
    }

    if (p.x < targetX)
      start = mid + 0.1;
    else
      end = mid - 0.1;
  }
  return point;
}
