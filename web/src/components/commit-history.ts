import { FlakinessReport } from '@flakiness/report';
import type { Stats } from '@flakiness/server/common/stats/stats.js';
import { TimelineSplit } from '@flakiness/server/common/timeline/timelineSplit.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Task } from '@lit/task';
import { timeDay, timeFormat } from 'd3';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, queryAll } from 'lit/decorators.js';
import { Temporal } from 'temporal-polyfill';
import { api } from '../api.js';
import { LinkRenderer } from '../contexts.js';
import { assert, loadAllPages } from '../utils.js';
import { linkStyles } from './cssstyles.js';
import { FKCommitStats } from './fk-commitstats.js';

const formatCommitHeader = timeFormat("Commits on %b %e, %Y");

const PAGE_SIZE = 20; // As github does this

@customElement('commit-history')
export class CommitHistory extends LitElement {
  @property({ attribute: false }) project?: WireTypes.Project;
  @property({ attribute: false }) head?: WireTypes.Ref;
  @property({ attribute: false }) since?: Date;
  @property({ attribute: false }) until?: Date;
  @property({ attribute: false }) testId?: Stats.TestId;
  @property({ attribute: false }) split?: TimelineSplit;
  @property({ attribute: false }) runlinker?: LinkRenderer<WireTypes.RunStats>;

  @queryAll('fk-commitstats') private _commitStatsElements?: FKCommitStats[];

  private _allCommitStatsTask = new Task(this, {
    args: () => [
      this.project, this.head, this.testId, this.split, this.since, this.until,
    ] as const,
    task: async ([project, headRef, testId, timelineSplit, since, until], { signal }) => {
      assert(project && headRef && timelineSplit && since && until);
      const commitStats: WireTypes.CommitStats[] = await loadAllPages(pageOptions => api.history.commitStats.POST({
        commitOptions: {
          head: headRef.commit.commitId,
          sinceTimestamp: since.getTime() as FlakinessReport.UnixTimestampMS,
          untilTimestamp: until.getTime() as FlakinessReport.UnixTimestampMS,
        },
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        timelineSplit: timelineSplit.serialize(),
        testId,
        timeZoneId: Temporal.Now.timeZoneId(),
        regressionWindowDays: project.regressionWindowDays,
        pageOptions: pageOptions,
      }, { signal }));
      return commitStats;
    }
  });

  private _renderCommitStats(allCommitStats?: WireTypes.CommitStats[]) {
    if (!allCommitStats)
      return nothing;

    const baselines = new Map<Stats.CommitId, number>();

    let lastDuration: number|undefined;
    for (const c of allCommitStats.toReversed()) {
      if (c.durationMs === undefined)
        continue;
      if (lastDuration)
        baselines.set(c.commit.commitId, lastDuration);
      lastDuration = c.durationMs;
    }

    const days: {
      day: Date,
      commitStats: WireTypes.CommitStats[],
    }[] = [];
    for (const commitStat of allCommitStats) {
      const day = timeDay.floor(new Date(commitStat.commit.timestamp));
      if (!days.length || +days.at(-1)!.day !== +day)
        days.push({ day, commitStats: []});
      days.at(-1)!.commitStats.push(commitStat);
    }
    return html`
      ${days.map(({ day, commitStats }) => html`
        <h-box class=day-title>
          <sl-icon library=boxicons name='bx-git-commit'></sl-icon>
          <div class=day-date>${formatCommitHeader(day)}</div>
        </h-box>
        <h-box>
          <div class=divider>
            <div class=line></div>
          </div>
          <div class=day-commits>
            ${commitStats.map(stats => html`
              <fk-commitstats
                .stats=${stats}
                .baseline=${baselines.get(stats.commit.commitId)}
                .project=${this.project}
                .runlinker=${this.runlinker}
              ></fk-commitstats>
            `)}
          </div>
        </h-box>
      `)}
    `;
  }

  override render() {
    const commitStats = this._allCommitStatsTask.value;
    if (!commitStats || !commitStats.length) {
      return html`
        <h1 style="color: var(--fk-color-border);">No Commits</h1>
      `
    }

    return html`
    <v-box>
      <commits-minimap
        .header=${`Tested ${commitStats.filter(x => x.runs.length > 0).length} out of ${commitStats.length} commits`}
        .commitStats=${commitStats.toReversed()}
        @fk-select=${(e: CustomEvent<WireTypes.CommitStats[]>) => {
          const selectedCommitIds = new Set(e.detail.map(e => e.commit.commitId));
          const selectedElements = Array.from(this._commitStatsElements ?? []).filter(e => e.stats && selectedCommitIds.has(e.stats.commit.commitId));
          if (!selectedElements.length)
            return;
          selectedElements[0].scrollIntoView({ behavior: 'smooth' });
          for (const e of selectedElements) {
            e.animate([
              { backgroundColor: 'var(--sl-color-warning-200)' },
              { backgroundColor: 'transparent' },
            ], {
              duration: 2500,
              iterations: 1,
              easing: 'ease-in-out'
            });
          }
        }}
      ></commits-minimap>
      ${this._renderCommitStats(commitStats)}
      <sl-divider></sl-divider>
    </v-box>
    `;
  }

  static styles = [linkStyles, css`
    commits-minimap {
      background: white;
      z-index: 100;
    }

    fk-commitstats {
      grid-column: 1/-1;
      border: none;
      border-radius: 0;
    }

    fk-commitstats + fk-commitstats {
      border-top: 1px solid var(--fk-color-border);
    }

    .day-title {
      color: var(--sl-color-neutral-500);
      margin: var(--sl-spacing-x-small) 0;
    }

    .day-navigation {
      flex: auto;
      align-items: end;
      justify-content: center;
    }

    .day-commits {
      flex: auto;
      border: 1px solid var(--fk-color-border);
      border-radius: var(--sl-border-radius-large);
      overflow: auto;
    }

    .divider {
      align-self: stretch;
      width: 14px;
      display: flex;
      align-items: stretch;
      justify-content: center;

      .line {
        width: 2px;
        --spill: var(--sl-spacing-small);
        margin-top: calc(0px - var(--spill));
        height: calc(100% + 2 * var(--spill));
        background: color-mix(in srgb, var(--sl-color-neutral-100) 50%, var(--sl-color-neutral-200) 50%);
      }
    }
  `];
}
