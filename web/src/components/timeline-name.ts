import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { FlakinessReport } from '@flakiness/report';
import { Timeline } from '@flakiness/server/common/timeline/timeline.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { classMap } from 'lit/directives/class-map.js';
import { contexts } from '../contexts.js';
import { githubLinks } from '../githubLinks.js';
import { linkStyles } from './cssstyles.js';

@customElement('timeline-name')
export class TimelineName extends LitElement {

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @property({ attribute: false }) timeline?: WireTypes.JSONTimeline;
  @property({ attribute: false }) href?: string|Function;

  override render() {
    if (!this.timeline)
      return nothing;

    const description = Timeline.deserialize(this.timeline).description();
    return html`
      <h-box class=content>
        <v-box style="gap: var(--sl-spacing-2x-small);">
          <a class=${classMap({ clickable: !!this.href })} href=${typeof this.href === 'string' ? this.href : nothing} @click=${typeof this.href === 'function' ? this.href : nothing}>
            <h-box class=timeline-name>
              ${description.system ? html`<span>[${description.system}]</span>` : nothing}
              <div style="flex: none;">${(description.name ?? '').trim().length ? description.name : html`<span class=noname>[no name]</span>`}</div>
              ${description.metadata ? html`<span>(${description.metadata})</span>` : nothing}
            </h-box>
          </a>
          ${description.configPath ? html`
            <a-ext class=configpath href=${this._project ? githubLinks.fileUrl(this._project, 'main' as FlakinessReport.CommitId, description.configPath) : nothing}>${description.configPath}</a-ext>
          ` : nothing}
        </v-box>
      </h-box>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      flex: none;
    }

    .clickable {
      cursor: pointer;
    }

    sl-icon {
      color: var(--sl-color-neutral-500);
    }

    .timeline-name {
      gap: var(--sl-spacing-2x-small);
      display: flex;
      font-weight: var(--sl-font-weight-semibold);

      &[selected] {
        background-color: var(--fk-color-highlight);
      }

      & > * {
        text-overflow: ellipsis;
        overflow: hidden;
        min-width: 0;
      }
    }

    .noname {
      color: var(--sl-color-neutral-500);
    }

    .configpath {
      color: var(--sl-color-neutral-500);
      font-size: var(--sl-font-size-small);
    }
  `]
}
