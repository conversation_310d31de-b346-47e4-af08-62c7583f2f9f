import { FlakinessReport } from '@flakiness/report';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { SlDropdown, SlShowEvent } from '@shoelace-style/shoelace';
import { timeDay } from 'd3';
import { css, html, LitElement, PropertyValues } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { api } from '../api.js';
import { contexts } from '../contexts.js';
import { assert, commitLast7Days, commitPreviousWeek, commitWeek, consumeDOMEvent } from '../utils.js';
import { CalendarRange, CalendarRangeHighlighter } from './calendar-range.js';
import { linkStyles } from './cssstyles.js';

@customElement('head-calendar')
class HEADCalendar extends LitElement {
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) headRef?: WireTypes.Ref;
  @property({ attribute: false }) since?: Date;
  @property({ attribute: false }) until?: Date;
  @property({ attribute: false }) maxRangeDays?: number;

  @query('calendar-range') private _calendarRange?: CalendarRange;
  @query('sl-dropdown') private _calendarDropdown?: SlDropdown;

  private _highlighter = new Task(this, {
    args: () => [this._project, this.headRef] as const,
    task: ([project, headRef]) => {
      assert(project && headRef);
      const result: CalendarRangeHighlighter = async (days, signal) => {
        const response = await api.report.countUploadedRunsForCommits.POST({
          orgSlug: project.org.orgSlug,
          projectSlug: project.projectSlug,
          commitOptions: days.map(day => ({
            head: headRef.commit.commitId,
            sinceTimestamp: +day.since as FlakinessReport.UnixTimestampMS,
            untilTimestamp: +day.until as FlakinessReport.UnixTimestampMS,
          }))
        }, { signal });
        return response.map(reports => reports ? `${reports} runs` : undefined);
      }
      return result;
    }
  })

  render() {
    const since = this.since;
    const until = this.until;
    return html`
      <sl-dropdown placement=bottom-start distance=10 @sl-show=${(event: SlShowEvent) => {
        // We have to guard against inner sl-tooltips that might show and hide.
        if (until && event.target === this._calendarDropdown)
          this._calendarRange?.revealDate(until);
      }} @sl-hide=${(event: SlShowEvent) => {
        // We have to guard against inner sl-tooltips that might show and hide.
        if (event.target === this._calendarDropdown)
          this._calendarRange?.cancelSelection();
      }}>
        <sl-icon-button slot="trigger" name=calendar4-range>          
        </sl-icon-button>
        <v-box class=calendar-picker>
          <calendar-range
            @rangeend=${(event: CustomEvent<Date>) => {
              if (!this._calendarRange || !this._calendarRange.since || !this._calendarRange.until)
                return;
              consumeDOMEvent(event);
              this._setCalendarRange({
                since: this._calendarRange.since,
                until: this._calendarRange.until,
              });
            }}
            .highlighter=${this._highlighter.value}
            .maxRangeDays=${this.maxRangeDays}
            .since=${since}
            .until=${until}
            .max=${new Date()}
          ></calendar-range>
          <h-box>
            <sl-button-group>  
              <sl-button @click=${() => {
                const headCommit = this.headRef?.commit;
                if (headCommit)
                  this._setCalendarRange(commitLast7Days(headCommit));            
              }}>Last 7 days</sl-button>
              <sl-button @click=${() => {
                const headCommit = this.headRef?.commit;
                if (headCommit)
                  this._setCalendarRange(commitWeek(headCommit));            
              }}>HEAD week</sl-button>
              <sl-button @click=${() => {
                const headCommit = this.headRef?.commit;
                if (headCommit)
                  this._setCalendarRange(commitPreviousWeek(headCommit));            
              }}>Previous week</sl-button>
            </sl-button-group>
            <x-filler></x-filler>
            <sl-icon-button name=crosshair @click=${() => {
              const headCommit = this.headRef?.commit;
              if (headCommit)
                this._calendarRange?.revealDate(new Date(headCommit.timestamp));
            }}></sl-icon-button>
          </h-box>
        </v-box>
      </sl-dropdown>
    `;
  }

  protected firstUpdated(_changedProperties: PropertyValues): void {
    if (!this.since && !this.until && this.headRef)
      this._calendarRange?.revealDate(new Date(this.headRef.commit.timestamp));
  }

  private _dispatchEvent() {
    if (!this.headRef)
      return;
    if (this.since && this.until) {
      this.dispatchEvent(new CustomEvent<{ since: Date, until: Date }>('fk-select', {
        detail: {
          since: this.since,
          until: this.until,
        }
      }));
    }
  }

  private _setCalendarRange({ since, until }: { since: Date, until: Date }) {
    if (!this.headRef)
      return;
    this.since = timeDay.floor(since);
    this.until = timeDay.ceil(until);
    this._calendarRange?.revealDate(until);
    this._dispatchEvent();
  }

  static styles = [linkStyles, css`
    .calendar-picker {
      background-color: white;
      padding: var(--sl-spacing-small);
      border: 1px solid var(--fk-color-border);
      box-shadow: var(--fk-shadow-floating-small);
      border-radius: var(--sl-border-radius-large);
    }
  `];
}
