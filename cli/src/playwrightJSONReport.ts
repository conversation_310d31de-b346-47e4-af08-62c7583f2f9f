import { ExternalAttachment } from '@flakiness/sdk';
import type { JSONReport, JSONReportSpec, JSONReportSuite, JSONReportTest, JSONReportTestResult, JSONReportTestStep, Location, TestError, TestStatus } from '@playwright/test/reporter';
import debug from 'debug';
import { posix as posixPath } from 'path';
import { FlakinessReport as FK, ReportUtils } from '../../report/sdk/src/reportutils.js';
import { computeGitRoot, createEnvironments, existsAsync, getOSInfo, gitCommitInfo, gitFilePath, inferRunUrl, NormalizedPath, normalizePath, parseDurationMS, parseStringDate, sha1Buffer, sha1File, stripAnsi } from './utils.js';

const dlog = debug('flakiness:json-report');

type ProcessingContext = {
  projectId2environmentIdx: Map<string, number>,
  testBaseDir: NormalizedPath,
  gitRoot: NormalizedPath,
  attachments: Map<string, ExternalAttachment>,
  extractAttachments: boolean,
  unaccessibleAttachmentPaths: string[],
}

type ParseOptions = {
  extractAttachments: boolean,
}

export namespace PlaywrightJSONReport {
  export type ReportMetadata = {
    gitRoot: NormalizedPath,
    commitId: string,
    osName?: string,
    arch?: string,
    osVersion?: string,
    runURL?: string,
  };

  export function collectMetadata(somePathInsideProject = process.cwd()): ReportMetadata {
    const commitId = gitCommitInfo(somePathInsideProject);
    const osInfo = getOSInfo();

    const metadata: ReportMetadata = {
      gitRoot: computeGitRoot(somePathInsideProject),
      commitId: commitId,
      osName: osInfo.name,
      arch: osInfo.arch,
      osVersion: osInfo.version,
      runURL: inferRunUrl(),
    };

    dlog(`metadata directory: ${somePathInsideProject}`);
    dlog(`metadata: ${JSON.stringify(metadata)}`)
    dlog(`commit info: ${JSON.stringify(commitId)}`);
    dlog(`os info: ${JSON.stringify(osInfo)}`);

    return metadata;
  }

  export async function parse(metadata: ReportMetadata, jsonReport: JSONReport, options: ParseOptions): Promise<{ report: FK.Report, attachments: ExternalAttachment[], unaccessibleAttachmentPaths: string[] }> {
    const context: ProcessingContext = {
      projectId2environmentIdx: new Map(),
      testBaseDir: normalizePath(jsonReport.config.rootDir),
      gitRoot: metadata.gitRoot,
      attachments: new Map(),
      unaccessibleAttachmentPaths: [],
      extractAttachments: options.extractAttachments,
    };

    const configPath = jsonReport.config.configFile ? gitFilePath(context.gitRoot, normalizePath(jsonReport.config.configFile)) : undefined;

    const report: FK.Report = {
      category: FK.CATEGORY_PLAYWRIGHT,
      commitId: metadata.commitId as FK.CommitId,
      configPath,
      url: metadata.runURL,
      environments: [],
      suites: [],
      opaqueData: jsonReport.config,
      unattributedErrors: jsonReport.errors.map(error => parseJSONError(context, error)),

      // The report.stats is a releatively new addition to Playwright's JSONReport,
      // so we have to polyfill with some reasonable values when it's missing.
      duration: jsonReport.stats?.duration && jsonReport.stats?.duration > 0 ? parseDurationMS(jsonReport.stats.duration) : 0 as FK.DurationMS,
      startTimestamp: jsonReport.stats && jsonReport.stats.startTime ? parseStringDate(jsonReport.stats.startTime) : Date.now() as FK.UnixTimestampMS,
    };

    report.environments = [...createEnvironments(jsonReport.config.projects).values()];
    for (let envIdx = 0; envIdx < report.environments.length; ++envIdx)
      context.projectId2environmentIdx.set(jsonReport.config.projects[envIdx].id, envIdx);

    report.suites = await Promise.all(jsonReport.suites.map(suite => parseJSONSuite(context, suite)));
    return {
      report: ReportUtils.dedupeSuitesTestsEnvironments(report),
      attachments: [...context.attachments.values()],
      unaccessibleAttachmentPaths: context.unaccessibleAttachmentPaths
    };
  }
}

async function parseJSONSuite(context: ProcessingContext, jsonSuite: JSONReportSuite) {
  let type: FK.SuiteType = 'suite';
  if (jsonSuite.column === 0 && jsonSuite.line === 0)
    type = 'file';
  else if (!jsonSuite.title)
    type = 'anonymous suite';

  const suite: FK.Suite = {
    type,
    title: jsonSuite.title,
    location: {
      file: gitFilePath(context.gitRoot, normalizePath(jsonSuite.file)),
      line: jsonSuite.line as FK.Number1Based,
      column: jsonSuite.column as FK.Number1Based,
    }
  }

  if (jsonSuite.suites && jsonSuite.suites.length)
    suite.suites = await Promise.all(jsonSuite.suites.map(suite => parseJSONSuite(context, suite)));
  if (jsonSuite.specs && jsonSuite.specs.length)
    suite.tests = await Promise.all(jsonSuite.specs.map(spec => parseJSONSpec(context, spec)));
  return suite;
}

async function parseJSONSpec(context: ProcessingContext, jsonSpec: JSONReportSpec) {
  // Consider the following scenario:
  // * file `tests.ts`: `export function defineTests() { test('foo', () => {} )}`
  // * file `a.spec.ts`: `defineTests();`
  // * file `b.spec.ts`: `defineTests();`

  // In this scenario, Playwright's JSON will yield 2 `JSONSpec` entries - even though the source code for test `foo`
  // is the same.
  //
  // Q: Shall we dedupe these entries?
  // A: right now we do nothing, but implement a special FlakinessReport.dedupeSuitesTestsEnvironments to deal with it.
  const test: FK.Test = {
    title: jsonSpec.title,
    tags: jsonSpec.tags,
    location: {
      file: gitFilePath(context.gitRoot, normalizePath(posixPath.join(context.testBaseDir, normalizePath(jsonSpec.file)))),
      line: jsonSpec.line as FK.Number1Based,
      column: jsonSpec.column as FK.Number1Based,
    },
    attempts: [],
  };

  // Each spec definition in the source code have the same set of tags, so no need to merge them.
  for (const jsonTest of jsonSpec.tests) {
    const environmentIdx = context.projectId2environmentIdx.get(jsonTest.projectId);
    if (environmentIdx === undefined)
      throw new Error('Inconsistent report - no project for a test found!');

    // Filter out all results that have no status; these happen if the test was sharded and did not
    // run on the shard.
    const testResults = jsonTest.results.filter(result => result.status !== undefined) as (JSONReportTestResult & { status: TestStatus })[];
    // If there are no results for the test, than ignore it; the test was not executed.
    if (!testResults.length)
      continue;
    test.attempts.push(...await Promise.all(testResults.map(jsonTestResult => parseJSONTestResult(context, jsonTest, environmentIdx, jsonTestResult))));
  }
  return test;
}

function createLocation(context: ProcessingContext, location: Location): FK.Location {
  return {
    file: gitFilePath(context.gitRoot, normalizePath(location.file)),
    line: location.line as FK.Number1Based,
    column: location.column as FK.Number1Based,
  };
}

async function parseJSONTestResult(context: ProcessingContext, jsonTest: JSONReportTest, environmentIdx: number, jsonTestResult: JSONReportTestResult) {
  const attachments: FK.Attachment[] = [];
  const attempt: FK.RunAttempt = {
    timeout: parseDurationMS(jsonTest.timeout),
    annotations: jsonTest.annotations.map(annotation => ({
      type: annotation.type,
      description: annotation.description,
      location: (annotation as any).location ? createLocation(context, (annotation as any).location) : undefined,
    })),
    environmentIdx,
    expectedStatus: jsonTest.expectedStatus,

    parallelIndex: jsonTestResult.parallelIndex,
    status: jsonTestResult.status as FK.TestStatus,
    errors: jsonTestResult.errors && jsonTestResult.errors.length ? jsonTestResult.errors.map(error => parseJSONError(context, error)) : undefined,

    stdout: jsonTestResult.stdout && jsonTestResult.stdout.length ? jsonTestResult.stdout : undefined,
    stderr: jsonTestResult.stderr && jsonTestResult.stderr.length ? jsonTestResult.stderr : undefined,

    steps: jsonTestResult.steps ? jsonTestResult.steps.map(jsonTestStep => parseJSONTestStep(context, jsonTestStep)) : undefined,

    startTimestamp: parseStringDate(jsonTestResult.startTime),
    duration: jsonTestResult.duration && jsonTestResult.duration > 0 ? parseDurationMS(jsonTestResult.duration) : 0 as FK.DurationMS,

    attachments,
  };
  if (context.extractAttachments) {
    await Promise.all((jsonTestResult.attachments ?? []).map(async jsonAttachment => {
      // If we cannot access attachment path, then we should skip this attachment, and add it to the "unaccessible" array.
      if (jsonAttachment.path && !(await existsAsync(jsonAttachment.path))) {
        context.unaccessibleAttachmentPaths.push(jsonAttachment.path);
        return;
      }

      const id = (jsonAttachment.path ? await sha1File(jsonAttachment.path) : sha1Buffer(jsonAttachment.body ?? '')) as FK.AttachmentId;
      context.attachments.set(id, {
        contentType: jsonAttachment.contentType,
        id,
        body: jsonAttachment.body ? Buffer.from(jsonAttachment.body) : undefined,
        path: jsonAttachment.path,
      });
      attachments.push({
        id,
        name: jsonAttachment.name,
        contentType: jsonAttachment.contentType,
      });
    }));
  }
  return attempt;
}

function parseJSONTestStep(context: ProcessingContext, jsonStep: JSONReportTestStep): FK.TestStep {
  const step: FK.TestStep = {
    // NOTE: jsonStep.duration was -1 in some playwright versions
    duration: parseDurationMS(Math.max(jsonStep.duration, 0)),
    title: jsonStep.title,
  };
  if (jsonStep.error)
    step.error = parseJSONError(context, jsonStep.error);
  if (jsonStep.steps)
    step.steps = jsonStep.steps.map(childJSONStep => parseJSONTestStep(context, childJSONStep));
  return step;
}

function parseJSONError(context: ProcessingContext, error: TestError): FK.ReportError {
  return {
    location: error.location ? createLocation(context, error.location) : undefined,
    message: error.message ? stripAnsi(error.message).split('\n')[0] : undefined,
    stack: error.stack,
    value: error.value,
  }
}
