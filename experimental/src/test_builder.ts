import { Stats } from '@flakiness/server/common/stats/stats.js';
import { StatsBuilder } from '@flakiness/server/common/stats/statsBuilder.js';
import { TestIndex } from '@flakiness/server/common/stats/testIndex.js';
import { compressTextAsync } from '@flakiness/shared/node/compression.js';
import assert from 'assert';
import fs from 'fs';
import { downloadArtifactIsMissing, EnhancedJSONReport, loadJSON, parseMicrosoftPlaywrightReport } from './utils.js';


assert(process.env.TESTDATA_JSON_REPORTS_PER_COMMIT_URL);
const commitsPath = await downloadArtifactIsMissing(process.env.TESTDATA_JSON_REPORTS_PER_COMMIT_URL);
const commits = await loadJSON(commitsPath) as {
  commitId: string,
  url: string,
}[];

const index = new TestIndex();
const builder = StatsBuilder.create(index);

let totalReports = 0;
let runId = 0 as Stats.RunId;
for (let i = 0; i < 10; ++i) {
  const commit = commits[i];
  const aPath = await downloadArtifactIsMissing(commit.url);

  const pwReports = await loadJSON(aPath).catch(e => {
    console.error(`error while loading json: ${aPath}`);
    console.error(e);
    return [];
  }) as EnhancedJSONReport[];

  for (let i = 0; i < pwReports.length; ++i) {
    const report = await parseMicrosoftPlaywrightReport(pwReports[i]);
    index.addReport(report);
    builder.addRun(runId++ as Stats.RunId, report);
  }
  const rawText = JSON.stringify(builder.jsonStats());
  const br = await compressTextAsync(rawText);
  totalReports += pwReports.length;
  const indexRaw = JSON.stringify(index.serialize());
  const indexCompressed = await compressTextAsync(indexRaw);
  console.log(`
#${i + 1}  Stats: ${bytesToHuman(rawText.length)} (br: ${bytesToHuman(br.length)}) - ${totalReports} reports (br: ${bytesToHuman(br.length / totalReports)} / report)
#${i + 1}  Index: ${bytesToHuman(indexRaw.length)} (br: ${bytesToHuman(indexCompressed.length)}) - ${index.countTests()} tests (br: ${bytesToHuman(indexCompressed.length / index.countTests())} / report)`);
}

await fs.promises.writeFile('/Users/<USER>/flakiness/server/a.json', JSON.stringify(builder.jsonStats()));

function bytesToHuman(bytes: number) {
  if (bytes < 1024)
    return bytes + ' B';
  bytes = bytes / 1024;
  if (bytes < 1024)
    return bytes.toFixed(2) + ' KB'
  bytes = bytes / 1024;
  return bytes.toFixed(2) + ' MB';
}
