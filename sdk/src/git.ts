import { FlakinessReport } from '@flakiness/flakiness-report';
import assert from 'assert';
import { NormalizedPath, normalizePath } from './pathutils.js';
import { shell } from './utils.js';


export function gitCommitInfo(gitRepo: string): FlakinessReport.CommitId {
  const sha = shell(`git`, ['rev-parse', 'HEAD'], {
    cwd: gitRepo,
    encoding: 'utf-8',
  });
  assert(sha, `FAILED: git rev-parse HEAD @ ${gitRepo}`);
  return sha.trim() as FlakinessReport.CommitId;
}

export function computeGitRoot(somePathInsideGitRepo: string): NormalizedPath {
  const root = shell(`git`, ['rev-parse', '--show-toplevel'], {
    cwd: somePathInsideGitRepo,
    encoding: 'utf-8',
  });
  assert(root, `FAILED: git rev-parse --show-toplevel HEAD @ ${somePathInsideGitRepo}`);
  return normalizePath(root);
}