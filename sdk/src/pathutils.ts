import { posix as posixPath, win32 as win32Path } from 'path';
import { FlakinessReport } from '../../report/sdk/src/reportutils.js';

/**
 * Brands a type by intersecting it with a type with a brand property based on
 * the provided brand string.
 */
type Brand<T, Brand extends string> = T & {
  readonly [B in Brand as `__${B}_brand`]: never;
};

const IS_WIN32_PATH = new RegExp('^[a-zA-Z]:\\\\', 'i');
const IS_ALMOST_POSIX_PATH = new RegExp('^[a-zA-Z]:/', 'i');

export type NormalizedPath = Brand<string, 'NormalizedPath'>;

/**
 * Different environments might yield different paths.
 * - Win32: D:\foo\bar.txt
 * - ALMOST_POSIX: D:/foo/bar.txt (this is how many folks on the internet end up converting Win32 paths to Posix paths, including Playwright.)
 * - Posix: /d/foo/bar.txt
 * Goal is to normalize them all to POSIX.
 * @param aPath a relative or absolute path.
 * @returns
 */
export function normalizePath(aPath: string): NormalizedPath {
  if (IS_WIN32_PATH.test(aPath)) {
    // convert Win32 path to ALMOST_POSIX path
    aPath = aPath.split(win32Path.sep).join(posixPath.sep);
  }
  if (IS_ALMOST_POSIX_PATH.test(aPath))
    return ('/' + aPath[0] + aPath.substring(2)) as NormalizedPath;
  return aPath as NormalizedPath;
}

export function gitFilePath(gitRoot: NormalizedPath, absolutePath: NormalizedPath): FlakinessReport.GitFilePath {
  return posixPath.relative(gitRoot, absolutePath) as FlakinessReport.GitFilePath;
}