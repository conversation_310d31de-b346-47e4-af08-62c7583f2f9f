import http from 'http';
import https from 'https';
import { retryWithBackoff } from './utils.js';

const FLAKINESS_DBG = !!process.env.FLAKINESS_DBG;
export function errorText(error: Error) {
  return FLAKINESS_DBG ? error.stack : error.message;
}

export namespace httpUtils {
  export function createRequest({ url, method = 'get', headers = {} }: {
    url: string,
    method?: 'get' | 'post' | 'put',
    headers?: Record<string, string|undefined>,
  }) {
    let resolve: (data: Buffer) => void;
    let reject: (e: Error) => void;
    const responseDataPromise = new Promise<Buffer>((a, b) => {
      resolve = a;
      reject = b;
    });

    const protocol = url.startsWith('https') ? https : http;
    headers = Object.fromEntries(Object.entries(headers).filter(([key, value]) => value !== undefined));
    const request = protocol.request(url, { method, headers }, (res: http.IncomingMessage) => {
      const chunks: Buffer[] = [];
      res.on('data', (chunk: Buffer) => chunks.push(chunk));
      res.on('end', () => {
        if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300)
          resolve(Buffer.concat(chunks));
        else
          reject(new Error(`Request to ${url} failed with ${res.statusCode}`));
      });
      res.on('error', error => reject(error));
    });
    request.on('error', reject!);
    return { request, responseDataPromise };
  }

  export async function getBuffer(url: string, backoff?: number[]): Promise<Buffer> {
    return await retryWithBackoff(async () => {
      const { request, responseDataPromise } = createRequest({ url });
      request.end();
      return await responseDataPromise;  
    }, backoff);
  }

  export async function getText(url: string, backoff?: number[]): Promise<string> {
    const buffer = await getBuffer(url, backoff);
    return buffer.toString('utf-8');
  }

  export async function getJSON(url: string) {
    return JSON.parse(await getText(url));
  }

  export async function postText(url: string, text: string, backoff?: number[]): Promise<Buffer> {
    const headers = {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(text) + ''
    };
    return await retryWithBackoff(async () => {
      const { request, responseDataPromise } = createRequest({ url, headers, method: 'post' });
      request.write(text);
      request.end();
      return await responseDataPromise;
    }, backoff);
  }

  export async function postJSON(url: string, json: any, backoff?: number[]): Promise<any> {
    const buffer = await postText(url, JSON.stringify(json), backoff);
    return JSON.parse(buffer.toString('utf-8'));
  }
}
