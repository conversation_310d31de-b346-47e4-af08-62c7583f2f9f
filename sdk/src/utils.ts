import { spawnSync, SpawnSyncOptionsWithStringEncoding } from 'child_process';

const FLAKINESS_DBG = !!process.env.FLAKINESS_DBG;
function errorText(error: Error) {
  return FLAKINESS_DBG ? error.stack : error.message;
}

export async function retryWithBackoff<T>(job: () => Promise<T>, backoff: number[] = []): Promise<T> {
  for (const timeout of backoff) {
    try {
      return await job();
    } catch (e: any) {
      if (e instanceof AggregateError)
        console.error(`[flakiness.io err]`, errorText(e.errors[0]));
      else if (e instanceof Error)
        console.error(`[flakiness.io err]`, errorText(e));
      else
        console.error(`[flakiness.io err]`, e);
      await new Promise(x => setTimeout(x, timeout));
    }
  }
  return await job();
}

const ansiRegex = new RegExp('[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))', 'g');
export function stripAnsi(str: string): string {
  return str.replace(ansiRegex, '');
}

export function shell(command: string, args?: string[], options?: SpawnSyncOptionsWithStringEncoding) {
  try {
    const result = spawnSync(command, args, { encoding: 'utf-8', ...options });
    if (result.status !== 0) {
      return undefined;
    }
    return (result.stdout as string).trim();
  } catch (e) {
    console.error(e);
    return undefined;
  }
}

