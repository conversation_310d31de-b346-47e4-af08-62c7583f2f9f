import { FlakinessReport } from '@flakiness/report';
import type { AppRouter } from '@flakiness/server/node/api.js';
import { TypedHTTP } from '@flakiness/shared/common/typedHttp.js';
import { compressTextAsync, compressTextSync } from '@flakiness/shared/node/compression.js';
import assert from 'assert';
import fs from 'fs';
import { URL } from 'url';
import { createServerAPI } from './serverapi.js';
import { httpUtils, retryWithBackoff } from './utils.js';

type ReportUploaderOptions = {
  flakinessEndpoint: string;
  flakinessAccessToken: string;
}

export interface ExternalAttachment {
  contentType: string;
  id: FlakinessReport.AttachmentId;
  path?: string;
  body?: Buffer;
}

export class ReportUploader {
  static optionsFromEnv(overrides?: Partial<ReportUploaderOptions>): ReportUploaderOptions|undefined {
    const flakinessAccessToken = overrides?.flakinessAccessToken ?? process.env['FLAKINESS_ACCESS_TOKEN'];
    if (!flakinessAccessToken)
      return undefined;
    const flakinessEndpoint = overrides?.flakinessEndpoint ?? process.env['FLAKINESS_ENDPOINT'] ?? 'https://flakiness.io';
    return { flakinessAccessToken, flakinessEndpoint };
  }

  static async upload(options: Partial<ReportUploaderOptions> & {
    report: FlakinessReport.Report,
    attachments: ExternalAttachment[],
    log?: (message: string) => void,
  }): Promise<{ errorMessage?: string } | undefined> {
    const uploaderOptions = ReportUploader.optionsFromEnv(options);
    if (!uploaderOptions) {
      if (process.env.CI)
        options.log?.(`[flakiness.io] Uploading skipped since no FLAKINESS_ACCESS_TOKEN is specified`);
      return undefined; 
    }
    const uploader = new ReportUploader(uploaderOptions);
    const upload = uploader.createUpload(options.report, options.attachments);
    const uploadResult = await upload.upload();
    if (!uploadResult.success) {
      options.log?.(`[flakiness.io] X Failed to upload to ${uploaderOptions.flakinessEndpoint}: ${uploadResult.message}`);
      return { errorMessage: uploadResult.message };
    }

    options.log?.(`[flakiness.io] ✓ Report uploaded ${uploadResult.message ?? ''}`);
    if (uploadResult.reportUrl)
      options.log?.(`[flakiness.io] ${uploadResult.reportUrl}`);
  }

  private _options: ReportUploaderOptions;
  constructor(options: ReportUploaderOptions) {
    this._options = options;
  }

  createUpload(report: FlakinessReport.Report, attachments: ExternalAttachment[]) {
    const upload = new ReportUpload(this._options, report, attachments);
    return upload;
  }
}

const HTTP_BACKOFF = [100, 500, 1000, 1000, 1000, 1000];

type UploadOptions = {
  syncCompression?: boolean;
}

class ReportUpload {
  private _report: FlakinessReport.Report;
  private _attachments: ExternalAttachment[];
  private _options: ReportUploaderOptions;
  private _api: TypedHTTP.Client<AppRouter>;

  constructor(options: ReportUploaderOptions, report: FlakinessReport.Report, attachments: ExternalAttachment[]) {
    this._options = options;
    this._report = report;
    this._attachments = attachments;
    // A client that retries requests if they fail.
    this._api = createServerAPI(this._options.flakinessEndpoint, { retries: HTTP_BACKOFF, auth: this._options.flakinessAccessToken });
  }

  async upload(options?: UploadOptions): Promise<{ success: boolean, message?: string, reportUrl?: string }> {
    const response = await this._api.run.startUpload.POST({
      attachmentIds: this._attachments.map(attachment => attachment.id),
    }).then(result => ({ result, error: undefined })).catch(e => ({ result: undefined, error: e }));

    if (response?.error || !response.result)
      return { success: false, message: `flakiness.io returned error: ${response.error.message}`}

    await Promise.all([
      this._uploadReport(JSON.stringify(this._report), response.result.report_upload_url, options?.syncCompression ?? false),
      ...this._attachments.map(attachment => {
        const uploadURL = response.result.attachment_upload_urls[attachment.id];
        if (!uploadURL)
          throw new Error('Internal error: missing upload URL for attachment!');
        return this._uploadAttachment(attachment, uploadURL, options?.syncCompression ?? false);
      }),
    ]);
  
    const response2 = await this._api.run.completeUpload.POST({
      upload_token: response.result.upload_token,
    }).then(result => ({ result, error: undefined })).catch(e => ({ error: e, result: undefined }));

    const url = response2?.result?.report_url ? new URL(response2?.result.report_url, this._options.flakinessEndpoint).toString() : undefined;
    return { success: true, reportUrl: url };
  }

  private async _uploadReport(data: string, uploadUrl: string, syncCompression: boolean) {
    const compressed = syncCompression ? compressTextSync(data) : await compressTextAsync(data);
    const headers = {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(compressed) + '',
      'Content-Encoding': 'br',
    };
    await retryWithBackoff(async () => {
      const { request, responseDataPromise } = httpUtils.createRequest({
        url: uploadUrl,
        headers,
        method: 'put'
      });
      request.write(compressed);
      request.end();
      await responseDataPromise;
    }, HTTP_BACKOFF);
  }

  private async _uploadAttachment(attachment: ExternalAttachment, uploadUrl: string, syncCompression: boolean) {
    const mimeType = attachment.contentType.toLocaleLowerCase().trim();
    const compressable = mimeType.startsWith('text/')
      || mimeType.endsWith('+json')
      || mimeType.endsWith('+text')
      || mimeType.endsWith('+xml')
    ;
    // Stream file only if there's attachment path and we should NOT compress it.
    if (!compressable && attachment.path) {
      const attachmentPath = attachment.path;
      await retryWithBackoff(async () => {
        const { request, responseDataPromise } = httpUtils.createRequest({
          url: uploadUrl,
          headers: {
            'Content-Type': attachment.contentType,
            'Content-Length': (await fs.promises.stat(attachmentPath)).size + '',
          },
          method: 'put'
        });
        fs.createReadStream(attachmentPath)
          .pipe(request);
        await responseDataPromise;
      }, HTTP_BACKOFF);
      return;
    }
    let buffer = attachment.body ? attachment.body : 
      attachment.path ? await fs.promises.readFile(attachment.path) :
      undefined;
    assert(buffer);

    const encoding = compressable ? 'br' : undefined;

    if (compressable)
      buffer = syncCompression ? compressTextSync(buffer) : await compressTextAsync(buffer);

    const headers = {
      'Content-Type': attachment.contentType,
      'Content-Length': Buffer.byteLength(buffer) + '',
      'Content-Encoding': encoding,
    };

    await retryWithBackoff(async () => {
      const { request, responseDataPromise } = httpUtils.createRequest({
        url: uploadUrl,
        headers,
        method: 'put'
      });
      request.write(buffer);
      request.end();
      await responseDataPromise;
    }, HTTP_BACKOFF);
  }
}
