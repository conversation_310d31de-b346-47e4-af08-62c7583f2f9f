import { TypedHTTP } from '@flakiness/shared/common/typedHttp.js';
import fs from 'fs';
import path from 'path';
import { z } from 'zod/v4';
import { FlakinessReport, ReportUtils } from '../../report/sdk/src/reportutils.js';
import { GitCommit, listLocalCommits } from './localGit.js';

export class ReportInfo {
  public report?: FlakinessReport.Report;

  attachmentIdToPath = new Map<FlakinessReport.AttachmentId, {
    contentType: string,
    id: string,
    path: string,
  }>();

  commits: GitCommit[] = [];

  constructor(private _options: {
    reportPath: string,
    attachmentsFolder: string
  }) {

  }

  async refresh() {
    const report = await fs.promises.readFile(this._options.reportPath, 'utf-8')
      .then(x => JSON.parse(x) as FlakinessReport.Report)
      .catch(e => undefined);

    if (!report) {
      this.report = undefined;
      this.commits = [];
      this.attachmentIdToPath = new Map();
      return;
    }

    if (JSON.stringify(report) === JSON.stringify(this.report))
      return;
    this.report = report;
    this.commits = await listLocalCommits(path.dirname(this._options.reportPath), report.commitId, 100);
    const attachmentsDir = this._options.attachmentsFolder;
    const { attachmentIdToPath, missingAttachments } = await resolveAttachmentPaths(report, attachmentsDir);
    if (missingAttachments.length) {
      const first = missingAttachments.slice(0, 3);
      for (let i = 0; i < 3 && i < missingAttachments.length; ++i)
        console.warn(`Missing attachment with id ${missingAttachments[i]}`);
      if (missingAttachments.length > 3)
        console.warn(`...and ${missingAttachments.length - 3} more missing attachments.`);
    }
    this.attachmentIdToPath = attachmentIdToPath;
  }
}

export type LocalReportAPIContext = {
  reportInfo: ReportInfo,
}

const t = TypedHTTP.Router.create<LocalReportAPIContext>();
export const localReportRouter = {
  ping: t.get({
    handler: async () => {
      return 'pong';
    }
  }),
  lastCommits: t.get({
    handler: async ({ ctx }) => {
      return ctx.reportInfo.commits;
    }
  }),
  report: {
    attachment: t.rawMethod('GET', {
      input: z.object({
        attachmentId: z.string().min(1).max(100).transform(id => id as FlakinessReport.AttachmentId),
      }),
      handler: async ({ ctx, input }) => {
        const idx = ctx.reportInfo.attachmentIdToPath.get(input.attachmentId);
        if (!idx)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND');
        const buffer = await fs.promises.readFile(idx.path);
        return TypedHTTP.ok(buffer, idx.contentType);
      },
    }),
    json: t.get({
      handler: async ({ ctx }) => {
        await ctx.reportInfo.refresh();
        return ctx.reportInfo.report;
      },
    }),
  },
};

// export type definition of API
export type LocalReportAppRouter = typeof localReportRouter;



async function resolveAttachmentPaths(report: FlakinessReport.Report, attachmentsDir: string) {
  const attachmentFiles = await listFilesRecursively(attachmentsDir);
  const filenameToPath = new Map(attachmentFiles.map(file => [path.basename(file), file]));
  const attachmentIdToPath = new Map<FlakinessReport.AttachmentId, {
    contentType: string,
    id: FlakinessReport.AttachmentId,
    path: string,
  }>();
  const missingAttachments = new Set<FlakinessReport.AttachmentId>();
  ReportUtils.visitTests(report, (test) => {
    for (const attempt of test.attempts) {
      for (const attachment of attempt.attachments ?? []) {
        const attachmentPath = filenameToPath.get(attachment.id);
        if (!attachmentPath) {
          missingAttachments.add(attachment.id);
        } else {
          attachmentIdToPath.set(attachment.id, {
            contentType: attachment.contentType,
            id: attachment.id,
            path: attachmentPath,
          });
        }
      }
    }
  });
  return { attachmentIdToPath, missingAttachments: Array.from(missingAttachments) };
}

async function listFilesRecursively(dir: string, result: string[] = []): Promise<string[]> {
  const entries = await fs.promises.readdir(dir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory())
      await listFilesRecursively(fullPath, result);
    else
      result.push(fullPath);
  }
  return result;
}