import { TypedHTTP } from '@flakiness/shared/common/typedHttp.js';
import { randomUUIDBase62 } from '@flakiness/shared/node/nodeutils.js';
import { createTypedHttpExpressMiddleware } from '@flakiness/shared/node/typedHttpExpress.js';
import bodyParser from 'body-parser';
import compression from 'compression';
import debug from 'debug';
import express, { NextFunction, Request, Response } from 'express';
import 'express-async-errors';
import http from 'http';
import { LocalReportAPIContext, localReportRouter, ReportInfo } from './localReportApi.js';

const logHTTPServer = debug('fk:http');

type ServerOptions = {
  reportPath: string,
  attachmentsFolder: string,
  endpoint: string,
  port: number,
}

export class LocalReportServer {
  static async create(options: ServerOptions) {
    const app = express();
    // Disable etag generation by default.
    app.set('etag', false);

    // First thing first - drop all unauthorized requests.
    const authToken = randomUUIDBase62();

    app.use(compression());
    app.use(bodyParser.json({ limit: 256 * 1024 }));

    app.use((req, res, next) => {
      // Quickly abort all nonauthorized requests.
      if (!req.path.startsWith('/' + authToken))
        throw TypedHTTP.HttpError.withCode('UNAUTHORIZED');

      res.setHeader("Access-Control-Allow-Headers", "*");
      res.setHeader("Access-Control-Allow-Origin", options.endpoint);
      res.setHeader("Access-Control-Allow-Methods", "*");

      if (req.method === "OPTIONS") {
        res.writeHead(204);
        res.end();
        return;
      }

      req.on('aborted', () => logHTTPServer(`REQ ABORTED ${req.method} ${req.originalUrl}`));
      res.on('close', () => {
        if (!res.headersSent) logHTTPServer(`RES CLOSED BEFORE SEND ${req.method} ${req.originalUrl}`);
      });
      next();
    });

    const reportInfo = new ReportInfo(options);
    app.use('/' + authToken, createTypedHttpExpressMiddleware<typeof localReportRouter, LocalReportAPIContext>({
      router: localReportRouter,
      createRootContext: async ({ req, res, input }) => ({ reportInfo }),
    }));

    app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
      if (err instanceof TypedHTTP.HttpError)
        return res.status(err.status).send({ error: err.message });
      // Handle other unexpected errors (consider them 500 errors)
      logHTTPServer(err);
      res.status(500).send({ error: 'Internal Server Error' });
    });

    const server = http.createServer(app);

    server.on('error', (err: any) => {
      if (err.code === 'ECONNRESET') {
        logHTTPServer('Client connection reset. Ignoring.');
        return;
      }
      throw err;
    });

    const port = await new Promise<number>(resolve => server.listen(options.port, () => {
      resolve((server.address() as any).port);
    }));

    return new LocalReportServer(server, port, authToken);
  }

  constructor(
    private _server: http.Server,
    private _port: number,
    private _authToken: string,
  ) {}

  authToken() {
    return this._authToken;
  }

  port() { return this._port; }

  async dispose() {
    await new Promise(x => this._server.close(x));
  }
}
