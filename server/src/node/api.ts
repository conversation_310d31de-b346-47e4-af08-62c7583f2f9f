import { AppClientId, Database, NewProductPlan, OrgAccessRole, Organization, OrgId, OrgPublicId, ProductPlanPublicId, Project, ProjectAccessRole, ProjectPublicId, User, UserPublicId, UserSession, UserSessionPublicId } from '@flakiness/database';
import { FlakinessReport } from '@flakiness/report';
import { TypedHTTP } from '@flakiness/shared/common/typedHttp.js';
import { xxHashObject } from '@flakiness/shared/common/utils.js';
import assert from 'assert';
import { randomUUID } from 'crypto';
import debug from 'debug';
import type express from 'express';
import jwt from 'jsonwebtoken';
import ms from 'ms';
import os from 'os';
import { Temporal } from 'temporal-polyfill';
import { z } from 'zod/v4';
import { pagedResponse, Sorter } from '../common/pagedResponse.js';
import { Ranges } from '../common/ranges.js';
import { Stats } from '../common/stats/stats.js';
import { Timeline } from '../common/timeline/timeline.js';
import { TimelineSplit } from '../common/timeline/timelineSplit.js';
import { WireTypes } from '../common/wireTypes.js';
import type { Authentication } from './authentication.js';
import { fromWireOrgRole, fromWireProjectRole, fromWireVisibility, toWireJob, toWireOrg, toWireOrgRole, toWireProject, toWireProjectRole, toWireUser, toWireUserSession } from './cachedDatabase.js';
import { createFlakinessAccessToken, parseAuthorizationToken } from './nodeutils.js';
import { S3Report } from './s3layout.js';
import { Services } from './services.js';

export type APIContext = {
  request: express.Request,
  auth: Authentication,
  services: Services,
  user?: User,
  userSession?: UserSession,
  project?: Project,
  projectRole?: ProjectAccessRole,
  org?: Organization,
  orgBillingStatus?: WireTypes.BillingStatus,
  orgRole?: OrgAccessRole,
  jwtSignSecret: string,
  preferences: {
    onlySuperusersCanCreateOrganizations: boolean,
  },
};

const t = TypedHTTP.Router.create<APIContext>();

// Public procedure will try to resolve context, where possible.
const publicProcedure = t;

// CI uploads are special since they resolve using flakinessAccessToken, and they
// have a special permissions due to billint.
const ciUpload = t.use(async ({ ctx, input }) => {
  const flakinessAccessToken = parseAuthorizationToken(ctx.request);
  if (!flakinessAccessToken)
    throw TypedHTTP.HttpError.withCode('UNAUTHORIZED', `FLAKINESS_ACCESS_TOKEN`);
  const project = await ctx.services.db.projects.getByAccessToken(flakinessAccessToken);
  const org = project ? await ctx.services.db.orgs.get(project.org_id) : undefined;

  if (!project || !org)
    throw TypedHTTP.HttpError.withCode('NOT_FOUND');
  const status = await ctx.services.stripeApp?.status(org);
  if (status?.restrictedCIUploads)
    throw TypedHTTP.HttpError.withCode('PAYMENT_REQUIRED');

  return { ...ctx, project, org };
});

// Authorized will throw if user is not authorized
const authorizedProcedure = t.use(async ({ ctx }) => {
  if (!ctx.user || !ctx.userSession)
    throw TypedHTTP.HttpError.withCode('UNAUTHORIZED');
  return { ...ctx, user: ctx.user, userSession: ctx.userSession };
});

// Proj read will throw if arguments don't resolve to a project accessible by a user.
const projReadIgnorePaymentStatus = publicProcedure.use(async ({ ctx }) => {
  const { projectRole, project, org, orgRole } = ctx;
  if (!project || !projectRole || !org)
    throw TypedHTTP.HttpError.withCode('NOT_FOUND');
  if (projectRole !== Database.projectAccessRole.editor && projectRole !== Database.projectAccessRole.viewer)
    throw TypedHTTP.HttpError.withCode('NOT_FOUND');
  return { ...ctx, projectRole, project, org, orgRole };
});

// Proj read will throw if arguments don't resolve to a project accessible by a user.
const projRead = projReadIgnorePaymentStatus.use(async ({ ctx }) => {
  const { orgBillingStatus } = ctx;
  if (orgBillingStatus?.restrictedProjectAccess)
    throw TypedHTTP.HttpError.withCode('PAYMENT_REQUIRED');
  return { ...ctx, orgBillingStatus };
});

const projReadWrite = authorizedProcedure.use(async ({ ctx }) => {
  const { user, projectRole, project, org, orgRole, orgBillingStatus } = ctx;
  if (!project || !org || !projectRole || !user)
    throw TypedHTTP.HttpError.withCode('NOT_FOUND');
  if (projectRole !== Database.projectAccessRole.editor)
    throw TypedHTTP.HttpError.withCode('NOT_FOUND');
  if (orgBillingStatus?.restrictedProjectAccess)
    throw TypedHTTP.HttpError.withCode('PAYMENT_REQUIRED');
  return { ...ctx, user, projectRole, project, org, orgRole, orgBillingStatus };
});

const orgMember = authorizedProcedure.use(async ({ ctx }) => {
  const { user, org, orgRole } = ctx;
  if (!org || !ctx.user || !orgRole)
    throw TypedHTTP.HttpError.withCode('FORBIDDEN');
  if (orgRole !== Database.orgAccessRole.admin && orgRole !== Database.orgAccessRole.member)
    throw TypedHTTP.HttpError.withCode('FORBIDDEN');
  return { ...ctx, user, org, orgRole };
});

const orgAdmin = authorizedProcedure.use(async (opts) => {
  const { ctx } = opts;
  const { user, org, orgRole } = ctx;
  if (orgRole !== Database.orgAccessRole.admin || !org || !user)
    throw TypedHTTP.HttpError.withCode('FORBIDDEN');

  return { ...ctx, user, org, orgRole };
});

const orgOwner = authorizedProcedure.use(async (opts) => {
  const { ctx } = opts;
  const { user, org, orgRole } = ctx;
  if (!org || !user || org.owner_id !== user.user_id)
    throw TypedHTTP.HttpError.withCode('FORBIDDEN');

  return { ...ctx, user, org, orgRole };
});

const superuserAccess = authorizedProcedure.use(async (opts) => {
  const { ctx } = opts;

  if (!ctx.auth.isSuperUser(ctx.user))
    throw TypedHTTP.HttpError.withCode('UNAUTHORIZED');

  return ctx;
});


const log = debug('fk:api');

function encodeReportId(reportId: S3Report.Id, signSecret: string): string {
  return jwt.sign(reportId, signSecret, { expiresIn: 60 * 60 /* 1 hour */});
}

function decodeReportId(token: string, signSecret: string): S3Report.Id {
  return jwt.verify(token, signSecret) as S3Report.Id;
}

const head = z.string().min(1).max(255);

const cutoffTestIndex = z.number().transform(x => x as Stats.TestIndex);
const commitOptions = z.object({
  // commit filtering options
  head,
  headOffset: z.optional(z.number()),
  maxCount: z.optional(z.number()),
  sinceTimestamp: z.optional(z.number()),
  untilTimestamp: z.optional(z.number()),
}).transform(value => value as WireTypes.ListCommitOptions );

// Must be all lowercase.
const slug = z.string().min(1).max(255).regex(/^[a-zA-Z0-9-._]+$/i).transform(x => x.toLowerCase());
const projectVisibility = z.enum(['public', 'private']);
const projectPublicId = z.string().min(1).max(100).transform(x => x as ProjectPublicId);
const userId = z.string().min(1).max(50).transform(arg => arg as UserPublicId);
const appClientId = z.string().min(1).max(50).transform(arg => arg as AppClientId);
const sessionId = z.string().min(1).max(50).transform(arg => arg as UserSessionPublicId);
const commitId = z.string().min(1).max(50).transform(arg => arg as Stats.CommitId);

const pageOptions = z.object({
  size: z.number().min(1).max(300).default(30),
  number: z.number().default(0),
});

// Old format was just UUID - it was 36 bytes long.
// New format is a base62-encoded string with `flakiness-io-` prefix. It's 35 symbols long.
const flakinessAccessToken = z.string().min(35).max(36);

const timelineSplit = z.object({
  splitByDefault: z.optional(z.boolean()),
  filters: z.object({
    system: z.optional(z.array(z.object({
      name: z.string().min(1).max(256),
      values: z.array(z.string().min(1).max(256)),
    }))),
    user: z.optional(z.array(z.object({
      name: z.string().min(1).max(256),
      values: z.array(z.string().min(1).max(256)),
    }))),
  }),
  inverse: z.object({
    system: z.optional(z.array(z.string().min(1).max(256))),
    user: z.optional(z.array(z.string().min(1).max(256))),
  }),
});

const timeline = z.object({
  system: z.array(z.object({
    name: z.string().min(1).max(256),
    value: z.string().min(1).max(256),
  })),
  user: z.array(z.object({
    name: z.string().min(1).max(256),
    value: z.string().min(1).max(256),
  })),
});

const testId = z.string().min(1).max(64).transform(x => x as Stats.TestId);

const timeZoneId = z.string().max(100).refine(
  (tz) => {
    try {
      new Intl.DateTimeFormat('en-US', { timeZone: tz });
      return true;
    } catch {
      return false;
    }
  },
  { message: 'Invalid IANA time zone ID' }
);
const reportOptionsBase = {
  orgSlug: slug,
  projectSlug: slug,
  commitOptions,
  timeZoneId,
  regressionWindowDays: z.number(),
  fql: z.optional(z.string().max(1024)),
  timelineSplit,
  customHistoryHead: z.optional(z.string().min(1).max(255)),
  dailyReportBreakdown: z.boolean(),
  historyBuckets: z.number(),
  acceptableFlakinessRate: z.number().min(0).max(1),
};

const historyOptionsBase = {
  orgSlug: slug,
  projectSlug: slug,
  commitOptions,
  timeZoneId,
  regressionWindowDays: z.number(),
  testId: z.optional(testId),
  timelineSplit,
};

export const appRouter = {
  run: {
    // The flakinessAccessToken automatically gives read-write permissions for project.
    startUpload: ciUpload.post({
      input: z.object({
        attachmentIds: z.array(z.string().min(1)),
      }),
      handler: async ({ ctx, input }) => {
        const consequtiveReportId = await ctx.services.db.projects.incReportCount(ctx.project.project_id) as Stats.RunId;
        const reportId: S3Report.Id = {
          projectPublicId: ctx.project.project_public_id,
          reportId: consequtiveReportId,
        };

        const { reportUploadURL, attachmentUploadURLs } = await ctx.services.uploadWorker.startUpload(reportId, input.attachmentIds);
        return {
          upload_token: encodeReportId(reportId, ctx.jwtSignSecret),
          report_upload_url: reportUploadURL,
          attachment_upload_urls: attachmentUploadURLs,
        };
      },
    }),
    completeUpload: ciUpload.post({
      input: z.object({ upload_token: z.string().min(1).max(2048) }),
      handler: async ({ ctx, input }) => {
        const reportId = decodeReportId(input.upload_token, ctx.jwtSignSecret);
        await ctx.services.uploadWorker.completeUpload(reportId);
        const project = await ctx.services.db.projects.getByPublicId(reportId.projectPublicId);
        const org = project ? await ctx.services.db.orgs.get(project.org_id) : undefined;
        return {
          report_url: org && project ? [
            '',
            org.org_slug,
            project.project_slug,
            'run',
            reportId.reportId
          ].join('/') : undefined,
        };
      },
    }),
    downloadURLs: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        runId: z.number(),
      }),
      handler: async ({ ctx, input }) => {
        const reportId: S3Report.Id = {
          projectPublicId: ctx.project.project_public_id,
          reportId: input.runId as Stats.RunId,
        };
        const reportIndex = await ctx.services.uploadWorker.reportIndex(ctx.project.project_public_id);
        if (!reportIndex)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND', `Failed to find project ${input.orgSlug}/${input.projectSlug}`);
        if (!reportIndex.hasRun(input.runId as Stats.RunId))
          throw TypedHTTP.HttpError.withCode('NOT_FOUND', `Failed to find run ${input.runId}`);
        return await ctx.services.s3objects.reports.downloadURLs(reportId);
      },
    }),
    json: projRead.rawMethod('GET', {
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        reportId: z.number(),
      }),
      handler: async ({ ctx, input }) => {
        const reportId: S3Report.Id = {
          projectPublicId: ctx.project.project_public_id,
          reportId: input.reportId as Stats.RunId,
        };
        const url = await ctx.services.s3objects.reports.readonlyReportUrl(reportId);
        return TypedHTTP.redirect<FlakinessReport.Report>(url);
      },
    }),
    attachment: projRead.rawMethod('GET', {
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        reportId: z.number(),
        attachmentId: z.string(),
      }),
      handler: async ({ ctx, input }) => {
        const reportId: S3Report.Id = {
          projectPublicId: ctx.project.project_public_id,
          reportId: input.reportId as Stats.RunId,
        };
        const url = await ctx.services.s3objects.reports.readonlyAttachmentUrl(reportId, input.attachmentId);
        return TypedHTTP.redirect(url);
      },
    }),
    playwrightTrace: projRead.rawMethod('GET', {
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        reportId: z.number(),
        attachmentId: z.string(),
      }),
      handler: async ({ ctx, input }) => {
        const reportId: S3Report.Id = {
          projectPublicId: ctx.project.project_public_id,
          reportId: input.reportId as Stats.RunId,
        };
        const attachmentUrl = await ctx.services.s3objects.reports.readonlyAttachmentUrl(reportId, input.attachmentId);
        const url = `https://trace.playwright.dev/?trace=${encodeURIComponent(attachmentUrl)}`;
        console.log(`url: ${url}`);
        return TypedHTTP.redirect(url);
      },
    }),
  },

  serverInfo: publicProcedure.get({
    handler: async ({ ctx }) => {
      const memory = ctx.services.managedMemoryCache.status();
      return {
        enabledBilling: !!ctx.services.stripeApp,
        githubAppPublicURL: ctx.services.githubApp.publicUrl().toString(),
        onlySuperusersCanCreateOrganizations: !!ctx.preferences.onlySuperusersCanCreateOrganizations,
        deploymentLicense: {
          validUntilMs: ctx.services.license.deadlineMs(),
        },
        uptimeMs: performance.now(),
        // NOTE: we consider hostname NOT to be a sensitive information,
        // thus exposing it to the world.
        hostname: os.hostname(),
        memory: {
          timestampMs: memory.timestamp,
          totalBytes: memory.totalBytes,
          usedBytes: memory.usedBytes,
        },
      } satisfies WireTypes.ServerInfo;
    }
  }),

  productPlans: {
    previewPlan: superuserAccess.post({
      input: z.object({
        name: z.string().min(1).max(255),
        orgSlug: z.optional(slug),
        seats: z.optional(z.number().min(0).max(99999)),
        trialDays: z.optional(z.number().min(0).max(720)),
        maxDataRetentionDays: z.optional(z.number().min(0).max(9999)),
        priceIdSeats: z.optional(z.string().min(1).max(255)),
        priceIdStorage: z.optional(z.string().min(1).max(255)),
        priceIdTestRuns: z.optional(z.string().min(1).max(255)),
      }),
      handler: async ({ ctx, input }) => {
        const planPublicId = randomUUID() as ProductPlanPublicId;
        const org = input.orgSlug ? await ctx.services.db.orgs.getBySlug(input.orgSlug) : undefined;
        return ctx.services.stripeApp?.toWirePlan({
          plan_public_id: planPublicId,
          name: input.name,
          org_public_id: org?.org_public_id ?? null,
          max_data_retention_days: input.maxDataRetentionDays ?? 365,
          seats: input.seats ?? null,
          seats_price_id: input.priceIdSeats ?? null,
          storage_price_id: input.priceIdStorage ?? null,
          testruns_price_id: input.priceIdTestRuns ?? null,
          trial_days: input.trialDays ?? null,
        });
      },
    }),
    create: superuserAccess.post({
      input: z.object({
        name: z.string().min(1).max(255),
        orgSlug: z.optional(slug),
        seats: z.optional(z.number().min(0).max(99999)),
        trialDays: z.optional(z.number().min(0).max(720)),
        maxDataRetentionDays: z.optional(z.number().min(0).max(9999)),
        priceIdSeats: z.optional(z.string().min(1).max(255)),
        priceIdStorage: z.optional(z.string().min(1).max(255)),
        priceIdTestRuns: z.optional(z.string().min(1).max(255)),
      }),
      handler: async ({ ctx, input }) => {
        const planPublicId = randomUUID() as ProductPlanPublicId;
        const org = input.orgSlug ? await ctx.services.db.orgs.getBySlug(input.orgSlug) : undefined;
        if (input.orgSlug && !org) {
          throw TypedHTTP.HttpError.withCode('NOT_FOUND', 'Org with given slug is not found');
        }
        const newPlan: NewProductPlan = {
          plan_public_id: planPublicId,
          name: input.name,
          org_public_id: org?.org_public_id,
          seats: input.seats,
          seats_price_id: input.priceIdSeats,
          storage_price_id: input.priceIdStorage,
          testruns_price_id: input.priceIdTestRuns,
          trial_days: input.trialDays,
          max_data_retention_days: input.maxDataRetentionDays ?? 365,
        };
        const plan = await ctx.services.db.productPlans.create(newPlan);
        return await ctx.services.stripeApp?.toWirePlan(plan);
      }
    }),

    delete: superuserAccess.post({
      input: z.object({
        planPublicId: z.string().min(1).max(50).transform(arg => arg as ProductPlanPublicId),
      }),
      handler: async ({ ctx, input }) => {
        const plan = await ctx.services.db.productPlans.getByPublicId(input.planPublicId);
        if (plan)
          await ctx.services.db.productPlans.delete(plan.plan_id);
      },
    }),

    listPrices: superuserAccess.get({
      handler: async ({ ctx }) => {
        if (!ctx.services.stripeApp)
          throw TypedHTTP.HttpError.withCode('METHOD_NOT_ALLOWED', 'billing is not enabled on the server');
        return await ctx.services.stripeApp.listPrices();
      },
    }),

    // Anybody can fetch generic product plans.
    listPlans: publicProcedure.get({
      handler: async ({ ctx }) => {
        return await ctx.services.stripeApp?.commonProductPlans() ?? [];
      },
    }),

    tailoredProductPlans: orgAdmin.get({
      input: z.object({
        orgSlug: slug,
      }),
      handler: async ({ ctx }) => {
        return await ctx.services.stripeApp?.orgPlans(ctx.org) ?? [];
      },
    })
  },

  administration: {
    runCronJobImmediately: superuserAccess.get({
      input: z.object({
        jobId: z.string(),
      }),
      handler: async ({ ctx, input }) => {
        await ctx.services.cron.runScheduledJobImmediately(input.jobId);
      },
    }),
    requestGitFetch: superuserAccess.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
      }),
      handler: async ({ ctx, input }) => {
        if (!ctx.project)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND');
        await ctx.services.gitWorker.scheduleBuild({ projectPublicId: ctx.project.project_public_id });
      },
    }),
    jobs: superuserAccess.get({
      input: z.object({
        pageSize: z.number(),
        queuedPage: z.number(),
        activePage: z.number(),
        archivedPage: z.number(),
      }),
      handler: async ({ ctx, input }) => {
        const pageSize = input.pageSize;
        const queuedOffset = input.queuedPage * pageSize;
        const activeOffset = input.activePage * pageSize;
        const archivedOffset = input.archivedPage * pageSize;
        const result = await ctx.services.db.queues.listJobs(pageSize, queuedOffset, activeOffset, archivedOffset);
        return {
          queued: {
            elements: result.queued.jobs.map(toWireJob),
            pageNumber: input.queuedPage,
            pageSize: pageSize,
            totalElements: result.queued.count,
            totalPages: Math.ceil(result.queued.count / pageSize),
          } satisfies WireTypes.PagedResponse<WireTypes.Job>,
          active: {
            elements: result.active.jobs.map(toWireJob),
            pageNumber: input.activePage,
            pageSize: pageSize,
            totalElements: result.active.count,
            totalPages: Math.ceil(result.active.count / pageSize),
          } satisfies WireTypes.PagedResponse<WireTypes.Job>,
          archived: {
            elements: result.archived.jobs.map(toWireJob),
            pageNumber: input.archivedPage,
            pageSize: pageSize,
            totalElements: result.archived.count,
            totalPages: Math.ceil(result.archived.count / pageSize),
          } satisfies WireTypes.PagedResponse<WireTypes.Job>,
        } ;
      },
    }),
  },

  report: {
    testIndex: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
      }),
      etag: async({ ctx }) => {
        const index = await ctx.services.uploadWorker.reportIndex(ctx.project.project_public_id);
        return index.testIndex().etag();
      },
      handler: async ({ ctx, input }) => {
        const index = await ctx.services.uploadWorker.reportIndex(ctx.project.project_public_id);
        return index.testIndex().serialize();
      },
    }),
    counters: projRead.post({
      input: z.object({
        ...reportOptionsBase,
      }),
      handler: async ({ ctx, input }) => {
        const counts = await ctx.services.query.reportCounters(ctx.project.project_public_id, input);
        return { ...counts, runs: 0 };
      },
    }),
    countUploadedRunsForCommits: projRead.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        commitOptions: z.array(commitOptions),
      }),
      handler: async ({ ctx, input }) => {
        const repo = await ctx.services.gitWorker.getRepo(ctx.project.project_public_id);
        const reportIndex = await ctx.services.uploadWorker.reportIndex(ctx.project.project_public_id);
        return input.commitOptions.map(commitOptions => {
          const commits = repo?.iterator(commitOptions.head).collect(commitOptions) ?? [];
          let reportsCount = 0;
          for (const commit of commits)
            reportsCount += Ranges.cardinality(reportIndex.commitRuns(commit.commitId));
          return reportsCount;
        });
      },
    }),
    allEnvironments: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        commitOptions,
        testId: z.optional(testId),
      }),
      handler: async ({ ctx, input }) => {
        if (input.testId)
          return await ctx.services.query.runEnvironmentsForTest(ctx.project.project_public_id, input.commitOptions, input.testId as Stats.TestId);
        return await ctx.services.query.runEnvironments(ctx.project.project_public_id, input.commitOptions);
      },
    }),

    dailyOutcomesExpanded: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        timeZoneId,
        head,
        regressionWindowDays: z.number(),
        dailyBucketsCount: z.number().min(0).max(15),
        cutoffTestIndex,
        timelines: z.array(timeline),
      }),
      etag: async ({ ctx, input }) => {
        const index = await ctx.services.uploadWorker.reportIndex(ctx.project.project_public_id);
        return xxHashObject({
          input, reportIndex: index.etag(),
        });
      },
      handler: async ({ ctx, input }) => {
        return await ctx.services.query.dailyOutcomesExpanded(ctx.project.project_public_id, input);
      },
    }),

    dailyOutcomes: projRead.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        acceptableFlakinessRatio: z.number().min(0).max(1),
        timeZoneId,
        sinceDay: z.string().transform(arg => Temporal.PlainDate.from(arg)),
        untilDay: z.string().transform(arg => Temporal.PlainDate.from(arg)),
        head,
        regressionWindowDays: z.number(),
        testId: z.optional(testId),
        timelineSplit,
      }),
      handler: async ({ ctx, input }) => {
        return await ctx.services.query.dailyOutcomes(ctx.project.project_public_id, input);
      },
    }),

    commitOutcomesExpanded: projRead.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        commitId,
        timeZoneId,
        regressionWindowDays: z.number(),
        cutoffTestIndex,
        timelines: z.array(timeline),
      }),
      handler: async ({ ctx, input }) => {
        return await ctx.services.query.commitOutcomesExpanded(ctx.project.project_public_id, input);
      },
    }),

    testStats: projRead.post({
      input: z.object({
        ...reportOptionsBase,
        sortOptions: z.object({
          axis: z.enum(['name', 'timeline_name', 'duration', 'outcome', 'history', 'duration_trend']),
          direction: z.enum(['asc', 'desc']),
        }),
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        const report = await ctx.services.query.testsReport(ctx.project.project_public_id, input);
        return report.tests.page(input.pageOptions, input.sortOptions.axis, input.sortOptions.direction);
      },
    }),

    getReportTestStats: projRead.get({
      input: z.object({
        ...reportOptionsBase,
        timeline,
        testId,
      }),
      handler: async ({ ctx, input }) => {
        const report = await ctx.services.query.testsReport(ctx.project.project_public_id, input);
        return report.findTestStats(input.testId, Timeline.deserialize(input.timeline));
      },
    }),

    timelineStats: projRead.post({
      input: z.object({
        ...reportOptionsBase,
        sortOptions: z.object({
          axis: z.enum(['name', 'outcome', 'total_time', 'unexpected', 'expected', 'skipped', 'flaked', 'regressed', 'history', 'duration_trend']),
          direction: z.enum(['asc', 'desc']),
        }),
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        const report = await ctx.services.query.testsReport(ctx.project.project_public_id, input);
        return report.timelines.page(input.pageOptions, input.sortOptions.axis, input.sortOptions.direction);
      },
    }),

    errorStats: projRead.post({
      input: z.object({
        ...reportOptionsBase,
        sortOptions: z.object({
          axis: z.enum(['name', 'timelines', 'tests',]),
          direction: z.enum(['asc', 'desc']),
        }),
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        const report = await ctx.services.query.testsReport(ctx.project.project_public_id, input);
        return report.errors.page(input.pageOptions, input.sortOptions.axis, input.sortOptions.direction);
      },
    }),
    tagStats: projRead.post({
      input: z.object({
        ...reportOptionsBase,
        sortOptions: z.object({
          axis: z.enum(['name', 'timelines', 'tests',]),
          direction: z.enum(['asc', 'desc']),
        }),
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        const report = await ctx.services.query.testsReport(ctx.project.project_public_id, input);
        return report.tags.page(input.pageOptions, input.sortOptions.axis, input.sortOptions.direction);
      },
    }),
    annotationStats: projRead.post({
      input: z.object({
        ...reportOptionsBase,
        sortOptions: z.object({
          axis: z.enum(['name', 'timelines', 'tests',]),
          direction: z.enum(['asc', 'desc']),
        }),
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        const report = await ctx.services.query.testsReport(ctx.project.project_public_id, input);
        return report.annotations.page(input.pageOptions, input.sortOptions.axis, input.sortOptions.direction);
      },
    }),
  },

  history: {
    commitStats: projRead.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        commitOptions,
        timeZoneId,
        regressionWindowDays: z.number(),
        testId: z.optional(testId),
        timelineSplit,
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        const stats = await ctx.services.query.commitsReport(ctx.project.project_public_id, input);
        return pagedResponse(stats, input.pageOptions);
      },
    }),
    listUnparsedReports: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        const repo = await ctx.services.uploadWorker.reportIndex(ctx.project.project_public_id);
        const unparsed = repo.unparsedReportErrors();
        const runIds = Array.from(unparsed.keys()).sort().reverse();
        const page = pagedResponse(runIds, input.pageOptions);
        return {
          ...page,
          elements: page.elements.map(runId => ({
            runId,
            error: unparsed.get(runId)!,
          })),
        }
      }
    }),
    listProcessedReports: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        const [repo, git] = await Promise.all([
          ctx.services.uploadWorker.reportIndex(ctx.project.project_public_id),
          ctx.services.gitWorker.getRepo(ctx.project.project_public_id),
        ]);
        const allRunIds = repo.allRunIds();
        const failedRunIds = Ranges.fromList(Array.from(repo.unparsedReportErrors().keys()));
        const allSuccessfulRunIds = Ranges.subtract(allRunIds, failedRunIds);
        const seq = Ranges.sequence(allSuccessfulRunIds);
        const runIt = seq.seek(seq.length - input.pageOptions.number * input.pageOptions.size - input.pageOptions.size);
        const runIds = Iterator.from(runIt).take(input.pageOptions.size).toArray().reverse();

        const elements: WireTypes.RunProcessingStatus[] = [];        
        for (const runId of runIds) {
          const commitId = repo.findCommitId(runId);
          const shard = commitId ? repo.getShard(commitId) : undefined;
          if (!shard || !commitId) {
            // weirdly, we did not find a matching commit id. Skip this run, log warning.
            log(`WARN: failed to fetch shard for ${input.orgSlug}/${input.projectSlug} and runId = ${runId}`)
            continue;
          }
          const statsAnalyzer = await ctx.services.statsWorker.statsAnalyzer(shard);
          const commitAnalyzer = statsAnalyzer.getCommitAnalyzer(commitId);
          const info = commitAnalyzer.runProcessingInfo(runId);
          if (info)
            elements.push(info);
          else
            log(`WARN: failed to compute runProcessingInfo for ${input.orgSlug}/${input.projectSlug} and runId = ${runId}`)
        }
        const result: WireTypes.PagedResponse<WireTypes.RunProcessingStatus> = {
          elements: elements,
          pageNumber: input.pageOptions.number,
          pageSize: input.pageOptions.size,
          totalElements: Ranges.cardinality(allRunIds),
          totalPages: Math.ceil(Ranges.cardinality(allRunIds) / input.pageOptions.size),
        };
        return result;
      },
    }),
    runStats: projRead.post({
      input: z.object({
        ...historyOptionsBase,
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        const report = await ctx.services.query.commitsReport(ctx.project.project_public_id, input);
        const runs = report.map(stats => stats.runs).flat();
        return pagedResponse(runs, input.pageOptions);
      },
    }),
  },

  deviceauth: {
    createRequest: publicProcedure.post({
      input: z.object({
        clientId: appClientId,
        name: z.string().min(1).max(100),
      }),
      handler: async ({ ctx, input }) => {
        const deadline = Date.now() + ms('5 minutes');
        const request = await ctx.services.db.uncachedDatabase().deviceAuthRequests.createRequest({
          clientId: input.clientId,
          deadlineMs: deadline,
          deviceName: input.name,
        });
        return {
          deadline,
          deviceCode: request.device_code,
          verificationUrl: [
            ``,
            `cli?code=${request.user_code.slice(0,4) + '-' + request.user_code.slice(4)}`,
          ].join('/'),
        };
      },
    }),

    getRequest: publicProcedure.get({
      input: z.object({
        userCode: z.string().min(1).max(50),
      }),
      handler: async ({ ctx, input }) => {
        const request = await ctx.services.db.uncachedDatabase().deviceAuthRequests.getByUserCode(input.userCode);
        if (!request || request.approver_id)
          return undefined;
        return {
          deviceName: request.device_name,
          deviceCode: request.device_code,
          clientId: request.client_id,
        } satisfies WireTypes.DeviceAuthRequest;
      },
    }),

    approveRequest: authorizedProcedure.post({
      input: z.object({
        deviceCode: z.string().min(1).max(50),
      }),
      handler: async ({ ctx, input }) => {
        const request = await ctx.services.db.uncachedDatabase().deviceAuthRequests.getByDeviceCode(input.deviceCode);
        if (!request)
          return undefined;
        if (request.approver_id)
          throw TypedHTTP.HttpError.withCode('UNAUTHORIZED', 'Cannot approve already-approved request');
        await ctx.services.db.uncachedDatabase().deviceAuthRequests.approve(request.request_id, ctx.user.user_id);
      },
    }),

    rejectRequest: authorizedProcedure.post({
      input: z.object({
        deviceCode: z.string().min(1).max(50),
      }),
      handler: async ({ ctx, input }) => {
        const request = await ctx.services.db.uncachedDatabase().deviceAuthRequests.getByDeviceCode(input.deviceCode);
        if (!request)
          return undefined;
        if (request.approver_id)
          throw TypedHTTP.HttpError.withCode('UNAUTHORIZED', 'Cannot reject already-approved request');
        await ctx.services.db.uncachedDatabase().deviceAuthRequests.delete(request.request_id);
      },
    }),

    getToken: publicProcedure.get({
      input: z.object({
        deviceCode: z.string().min(1).max(50),
      }),
      handler: async ({ ctx, input }) => {
        const request = await ctx.services.db.uncachedDatabase().deviceAuthRequests.getByDeviceCode(input.deviceCode);
        if (!request)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND', 'Request not found');
        return {
          token: await ctx.auth.createCLISession(request),
        };
      },
    }),
  },

  project: {
    checkProjectExists: publicProcedure.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
      }),
      handler: async ({ ctx }) => {
        return !!ctx.project;
      },
    }),

    pull: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        number: z.number(),
      }),
      handler: async ({ ctx, input }) => {
        const repo = await ctx.services.gitWorker.getRepo(ctx.project.project_public_id);
        const pulls = repo?.pullRequests() ?? [];
        return pulls.find(pull => pull.number === input.number);
      },
    }),

    pulls: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        prState: z.enum(['open', 'closed', 'merged', 'all']),
        testState: z.enum(['all', 'tested', 'untested']),
        sortOptions: z.object({
          axis: z.enum(['created',]),
          direction: z.enum(['asc', 'desc']),
        }),
        regressionWindowDays: z.number().min(0).max(30),
        timeZoneId,
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        // Dirty hack: every time someone requests pull requests, go and fetch them.
        // This is to circumvent absence of Github Web Hooks.
        await ctx.services.gitWorker.requestThrottledGitFetch({
          projectPublicId: ctx.project.project_public_id,
        });

        const repo = await ctx.services.gitWorker.getRepo(ctx.project.project_public_id);
        let pulls = repo?.pullRequests() ?? [];
        if (input.prState && input.prState !== 'all')
          pulls = pulls.filter(pr => pr.state === input.prState);
        if (input.testState === 'tested')
          pulls = pulls.filter(pr => !!pr.lastTestedCommit);
        else if (input.testState === 'untested')
          pulls = pulls.filter(pr => !pr.lastTestedCommit);

        const sorter = new Sorter(pulls, {
          'created': (pr1, pr2) => pr1.createdTimestamp - pr2.createdTimestamp,
        });
        const page = sorter.page(input.pageOptions, input.sortOptions.axis, input.sortOptions.direction);
        const result: WireTypes.PagedResponse<WireTypes.PullRequest> = {
          ...page,
          elements: await Promise.all(page.elements.map(async pr => {
            const commitStats = pr.lastTestedCommit ? await ctx.services.query.commitsReport(ctx.project.project_public_id, {
              commitOptions: {
                head: pr.lastTestedCommit,
                maxCount: 1,
              },
              regressionWindowDays: input.regressionWindowDays,
              timelineSplit: TimelineSplit.DEFAULT.serialize(),
              timeZoneId: input.timeZoneId,
              customHistoryHead: pr.baseSha,
            }) : undefined;
            return {
              ...pr,
              commitStats: commitStats?.at(0),
            } satisfies WireTypes.PullRequest;
          })),
        }
        return result;
      },
    }),

    findProject: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
      }),
      handler: async ({ ctx }) => {
        return toWireProject(toWireOrg(ctx.user, ctx.org, ctx.orgRole, ctx.orgBillingStatus), ctx.project, ctx.projectRole);
      },
    }),

    getProject: projRead.get({
      input: z.object({
        projectPublicId,
      }),
      handler: async ({ ctx, input }) => {
        return toWireProject(toWireOrg(ctx.user, ctx.org, ctx.orgRole, ctx.orgBillingStatus), ctx.project, ctx.projectRole);
      },
    }),

    listRuns: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        sinceTimestampMs: z.number(),
      }),
      handler: async ({ ctx, input }) => {
        const repo = await ctx.services.uploadWorker.reportIndex(ctx.project.project_public_id);
        return repo.listRunsSince(input.sinceTimestampMs)
      },
    }),

    fetchRepositoryStats: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
      }),
      handler: async ({ ctx }) => {
        const repo = await ctx.services.gitWorker.getRepo(ctx.project.project_public_id);
        return {
          commits: repo?.commitsSize(),
          branches: repo?.branches().length,
          pullRequests: repo?.pullRequests().length,
        };
      },
    }),

    filterBranches: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        pageOptions,
        filter: z.string().min(0).max(100),
      }),
      handler: async ({ ctx, input }) => {
        const repo = await ctx.services.gitWorker.getRepo(ctx.project.project_public_id);
        const allBranches = repo?.branches() ?? [];
        allBranches.sort((b1, b2) => b2.commit.timestamp - b1.commit.timestamp);
        const filter = input.filter;
        const filteredBranches = filter && filter.length ? allBranches.filter(b => b.name.toLowerCase().includes(filter)) : allBranches;
        return pagedResponse(filteredBranches, input.pageOptions);
      },
    }),

    resolveRef: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        head,
      }),
      handler: async ({ ctx, input }) => {
        const repo = await ctx.services.gitWorker.getRepo(ctx.project.project_public_id);
        if (!repo)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND');
        return repo.ref(input.head);
      }
    }),

    branches: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        pageOptions,
      }),
      handler: async ({ ctx, input }) => {
        const repo = await ctx.services.gitWorker.getRepo(ctx.project.project_public_id);
        const allBranches = repo?.branches() ?? [];
        allBranches.sort((b1, b2) => b2.commit.timestamp - b1.commit.timestamp);
        return repo ? {
          branches: pagedResponse(allBranches, input.pageOptions),
          defaultBranch: repo.defaultBranch(),
        } : undefined;
      },
    }),

    getCommit: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        head: z.string().min(1),
      }),
      handler: async ({ input, ctx }) => {
        const repo = await ctx.services.gitWorker.getRepo(ctx.project.project_public_id);
        return repo?.commit(input.head);
      },
    }),

    listCommits: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        commitOptions,
        pageOptions,
      }),
      handler: async ({ input, ctx }) => {
        const repo = await ctx.services.gitWorker.getRepo(ctx.project.project_public_id);
        const commits = repo?.iterator(input.commitOptions.head).collect(input.commitOptions) ?? [];
        return pagedResponse(commits, input.pageOptions);
      }
    }),

    delete: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
      }),
      handler: async ({ ctx }) => {
        await ctx.services.db.projects.delete(ctx.project.project_id);
        await ctx.services.s3gc.collectDeadProjects();
      },
    }),
    users: projRead.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
      }),
      handler: async ({ ctx, input }) => {
        const collaborators = await ctx.services.db.projectSharing.getUsers(ctx.project.project_id);
        
        return await Promise.all(collaborators.map(async ({ userId, accessRole }) => {
          const user = await ctx.services.db.users.get(userId);
          assert(user);
          return {
            user: toWireUser(user, ctx.auth.isSuperUser(user)),
            userRole: toWireProjectRole(accessRole),
          };
        }));
      },
    }),
    setProjectPreferredDataRetention: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        preferredDataRetentionDays: z.optional(z.number().min(1).max(99999)),
      }),
      handler: async ({ ctx, input }) => {
        const dataRetentionDays = input.preferredDataRetentionDays ?? Infinity;
        await ctx.services.db.projects.update(ctx.project.project_id, {
          preferred_data_retention_days: dataRetentionDays === Infinity ? null : dataRetentionDays,
        });
      },
    }),

    setProjectAcceptableFlakinessRatio: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        acceptableFlakinessRatio: z.number().min(0).max(1),
      }),
      handler: async ({ ctx, input }) => {
        await ctx.services.db.projects.update(ctx.project.project_id, {
          accepted_flakiness_ratio: input.acceptableFlakinessRatio,
        });
      },
    }),

    setProjectDefaultTimezone: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        defaultTimezone: z.string().max(64).optional(),
      }),
      handler: async ({ ctx, input }) => {
        await ctx.services.db.projects.update(ctx.project.project_id, {
          default_timezone: input.defaultTimezone,
        });
      },
    }),

    setProjectRegressionWindow: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        regressionWindowDays: z.number().min(0).max(30),
      }),
      handler: async ({ ctx, input }) => {
        await ctx.services.db.projects.update(ctx.project.project_id, {
          regression_window_days: input.regressionWindowDays,
        });
      },
    }),

    setProjectSourceWithGithubInstallation: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        sourceOwner: z.string(),
        sourceRepo: z.string(),
      }),
      handler: async ({ ctx, input }) => {
        const installationId = await ctx.services.githubApp.getInstallationId({
          user: ctx.user,
          owner: input.sourceOwner,
          repo: input.sourceRepo,
        });
        await ctx.services.db.projectSource.setProjectSource({
          projectId: ctx.project.project_id,
          sourceOwnerName: input.sourceOwner,
          sourceRepo: input.sourceRepo,
          installationId: String(installationId),
        });

        await ctx.services.db.projects.update(ctx.project.project_id, {
          source_last_fetch_http_status: null,
          source_last_fetch_timestamp_seconds: Math.floor(Date.now() / 1000),
        });
        await ctx.services.gitWorker.scheduleBuild({
          projectPublicId: ctx.project.project_public_id
        });
      },
    }),

    setProjectName: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        newProjectName: z.optional(z.string().min(1)),
        newProjectSlug: z.optional(slug),
      }),
      handler: async ({ ctx, input }) => {
        await ctx.services.db.projects.update(ctx.project.project_id, {
          project_name: input.newProjectName,
          project_slug: input.newProjectSlug,
        });
      },
    }),

    // This is for test only, but not too harmful for regular
    // users. So we will hide this option in the UI, but we don't
    // mind if someone will abuse this option via API.
    setProjectReportTimeAsUploadTimeForTest: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        reportTimeAsUploadTime: z.boolean(),
      }),
      handler: async ({ ctx, input }) => {
        await ctx.services.db.projects.update(ctx.project.project_id, {
          report_time_is_upload_time: input.reportTimeAsUploadTime,
        });
      },
    }),

    setProjectSourceWithGithubPAT: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        sourceOwner: z.string(),
        sourceRepo: z.string(),
        personalAccessToken: z.string(),
      }),
      handler: async ({ ctx, input }) => {
        await ctx.services.db.projectSource.setProjectSource({
          projectId: ctx.project.project_id,
          sourceOwnerName: input.sourceOwner,
          sourceRepo: input.sourceRepo,
          personalAccessToken: input.personalAccessToken,
        });
        await ctx.services.db.projects.update(ctx.project.project_id, {
          source_last_fetch_http_status: null,
          source_last_fetch_timestamp_seconds: Math.floor(Date.now() / 1000),
        });
        await ctx.services.gitWorker.scheduleBuild({
          projectPublicId: ctx.project.project_public_id,
        });
      },
    }),

    setVisibility: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        visibility: projectVisibility,
      }),
      handler: async ({ ctx, input }) => {
        const visibility = fromWireVisibility(input.visibility);
        await ctx.services.db.projects.update(ctx.project.project_id, { visibility });
      },
    }),

    shareProject: projReadWrite.post({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
        userId: userId,
        access: z.optional(z.enum(['viewer', 'editor'])),
      }),
      handler: async ({ ctx, input }) => {
        const role = input.access ? fromWireProjectRole(input.access) : undefined;
        const user = await ctx.services.db.users.getByPublicId(input.userId);
        if (!user)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND', 'user not found');
        await ctx.services.db.projectSharing.setAccess(ctx.project.project_id, user.user_id, role);
      },
    }),

    // Do show project metrics even if there's a "payment required" error.
    metrics: projReadIgnorePaymentStatus.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
      }),
      handler: async ({ ctx, input }) => {
        return await ctx.services.uploadWorker.projectMetrics(ctx.project.project_public_id);
      },
    }),

    /**
     * Only org members can fetch 
     */
    maxDataRetention: projReadWrite.get({
      input: z.object({
        orgSlug: slug,
        projectSlug: slug,
      }),
      handler: async ({ ctx, input }) => {
        const billing = await ctx.services.stripeApp?.status(ctx.org);
        return billing?.subscription?.maxDataRetentionDays;
      },
    })
  },

  github: {
    checkPATAccess: authorizedProcedure.get({
      input: z.object({
        owner: z.string(),
        repo: z.string(),
        accessToken: z.string(),
      }),
      handler: async ({ ctx, input }) => {
        return await ctx.services.githubApp.checkPATAccess({
          owner: input.owner,
          repo: input.repo,
          accessToken: input.accessToken, 
        });
      },
    }),
    checkAppAccess: authorizedProcedure.get({
      input: z.object({
        owner: z.string(),
        repo: z.string(),
      }),
      handler: async ({ ctx, input }) => {
        const installationId = await ctx.services.githubApp.getInstallationId({
          user: ctx.user,
          owner: input.owner,
          repo: input.repo,
        });
        return !!installationId;  
      },
    })
  },

  billing: {
    // Only org owner can fetch billing status.
    status: orgOwner.get({
      input: z.object({
        orgSlug: slug,
      }),
      handler: async ({ input, ctx }) => {
        if (!ctx.services.stripeApp)
          throw TypedHTTP.HttpError.withCode('METHOD_NOT_ALLOWED', 'billing is not enabled on the server');
        return await ctx.services.stripeApp.status(ctx.org);
      },
    }),

    // Only org owner can fetch all plans.
    customerPortal: orgOwner.get({
      input: z.object({
        orgSlug: slug,
      }),
      handler: async ({ input, ctx }) => {
        if (!ctx.services.stripeApp)
          throw TypedHTTP.HttpError.withCode('METHOD_NOT_ALLOWED', 'billing is not enabled on the server');
        return await ctx.services.stripeApp.customerPortalURL(ctx.org);
      },
    }),

    // Only org owner can change plan.
    setPlan: orgOwner.post({
      input: z.object({
        orgSlug: slug,
        planId: z.string().min(1).max(50),
      }),
      handler: async ({ input, ctx }) => {
        if (!ctx.services.stripeApp)
          throw TypedHTTP.HttpError.withCode('METHOD_NOT_ALLOWED', 'billing is not enabled on the server');
        const plan = await ctx.services.db.productPlans.getByPublicId(input.planId as ProductPlanPublicId);
        if (!plan)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND', 'given plan is not found');
        return await ctx.services.stripeApp.setPlan(ctx.org, plan);
      },
    })
  },

  organization: {
    delete: orgOwner.post({
      input: z.object({
        orgSlug: slug,
      }),
      handler: async ({ input, ctx }) => {
        await ctx.services.db.orgs.delete(ctx.org.org_id);
        await ctx.services.stripeApp?.scheduleSyncWithStripe();
        await ctx.services.s3gc.collectDeadProjects();
      },
    }),
    listMembers: orgMember.get({
      input: z.object({
        orgSlug: slug,
      }),
      handler: async ({ input, ctx }) => {
        const owner = await ctx.services.db.users.get(ctx.org.owner_id);
        assert(owner);
        const userIds = await ctx.services.db.orgSharing.getUsers(ctx.org.org_id);
        const users = await Promise.all(userIds.map(async ({ userId, accessRole }) => {
          const user = await ctx.services.db.users.get(userId);
          assert(user);
          return {
            accessRole: toWireOrgRole(user, ctx.org, accessRole),
            user: toWireUser(user, ctx.auth.isSuperUser(user)),
          }
        }));

        return {
          owner: toWireUser(owner, ctx.auth.isSuperUser(owner)),
          members: users,
        };
      },
    }),

    metrics: orgMember.get({
      input: z.object({
        orgSlug: slug,
      }),
      handler: async ({ ctx, input }) => {
        return await ctx.services.uploadWorker.orgMetrics(ctx.org.org_id);
      },
    }),
    setMembership: orgAdmin.post({
      input: z.object({
        orgSlug: slug,
        userId: userId,
        role: z.optional(z.enum(['member', 'admin'])),
      }),
      handler: async ({ input, ctx }) => {
        const role = input.role ? fromWireOrgRole(input.role) : undefined;
        const user = await ctx.services.db.users.getByPublicId(input.userId);
        if (!user)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND', 'user not found');
        if (user.user_id === ctx.org.owner_id)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND', 'cannot set membership for owner');

        await ctx.services.db.orgSharing.setAccess(ctx.org.org_id, user.user_id, role);
      },
    }),

    transfer: orgOwner.post({
      input: z.object({
        orgSlug: slug,
        newOwner: userId,
      }),
      handler: async ({ ctx, input }) => {
        const newOwner = await ctx.services.db.users.getByPublicId(input.newOwner);
        if (!newOwner)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND', `user with id "${input.newOwner}" does not exist`);
        await ctx.services.db.orgs.update(ctx.org.org_id, {
          owner_id: newOwner?.user_id,
        });
        // drop any sharing configured for the new owner.
        await ctx.services.db.orgSharing.setAccess(ctx.org.org_id, newOwner.user_id, undefined);
      }
    }),

    changeOrganizationSettings: orgAdmin.post({
      input: z.object({
        orgSlug: slug,
        newOrgName: z.optional(z.string().min(1).max(255)),
        newOrgSlug: z.optional(slug),
      }),
      handler: async ({ ctx, input }) => {
        await ctx.services.db.orgs.update(ctx.org.org_id, {
          org_name: input.newOrgName,
          org_slug: input.newOrgSlug,
        });
      }
    }),

    /**
     * Anybody can list projects inside the organization,
     * however, the list will reflect user permissions.
     */
    listProjects: publicProcedure.get({
      input: z.object({
        orgSlug: slug,
      }),
      handler: async ({ input, ctx }) => {
        if (!ctx.org)
          return [];
        const allProjectIds = await ctx.services.db.orgs.getProjects(ctx.org.org_id);
        const projects: WireTypes.Project[] = [];
        const wireOrg = toWireOrg(ctx.user, ctx.org, ctx.orgRole, ctx.orgBillingStatus);
        for (const projectId of allProjectIds) {
          const project = await ctx.services.db.projects.get(projectId);
          if (!project)
            continue;
          const role = await ctx.auth.getUserProjectRole(ctx.user, project);
          if (role)
            projects.push(toWireProject(wireOrg, project, role));
        }
        return projects;
      },
    }),

    createGithubPATProject: orgAdmin.post({
      input: z.object({
        orgSlug: slug,
        projectName: z.string().min(1).max(255),
        projectSlug: slug,
        visibility: z.optional(projectVisibility),
        github: z.object({
          owner: z.string(),
          repo: z.string(),
          accessToken: z.string(),
        })
      }),
      handler: async ({ ctx, input }) => {
        const project = await ctx.services.db.projects.create({
          flakiness_access_token: createFlakinessAccessToken(),
          org_id: ctx.org.org_id,
          project_slug: input.projectSlug,
          project_public_id: randomUUID() as ProjectPublicId,
          project_name: input.projectName,
          source_auth_type: Database.sourceAuthType.githubPat,
          source_owner_name: input.github.owner,
          source_repo_name: input.github.repo,
          visibility: fromWireVisibility(input.visibility),
        }, {
          pat: input.github.accessToken,
        });
        await ctx.services.gitWorker.scheduleBuild({
          projectPublicId: project.project_public_id,
        });
        return { success: true };
      },
    }),
    createGithubAppProject: orgAdmin.post({
      input: z.object({
        orgSlug: slug,
        projectName: z.string().min(1).max(255),
        projectSlug: slug,
        visibility: z.optional(projectVisibility),
        github: z.object({
          owner: z.string(),
          repo: z.string(),
        })
      }),
      handler: async ({ ctx, input }) => {
        const installationId = await ctx.services.githubApp.getInstallationId({
          user: ctx.user,
          owner: input.github.owner,
          repo: input.github.repo,
        });
        if (!installationId)
          return { success: false };
        const project = await ctx.services.db.projects.create({
          flakiness_access_token: createFlakinessAccessToken(),
          project_public_id: randomUUID() as ProjectPublicId,
          org_id: ctx.org.org_id,
          project_name: input.projectName,
          project_slug: input.projectSlug,
          visibility: fromWireVisibility(input.visibility),
          source_auth_type: Database.sourceAuthType.githubApp,
          source_owner_name: input.github.owner,
          source_repo_name: input.github.repo,
        }, {
          installationId: installationId + '',
        });
        await ctx.services.gitWorker.scheduleBuild({
          projectPublicId: project.project_public_id,
        });
        return { success: true };
      },
    })
  },

  user: {
    whoami: authorizedProcedure.get({
      handler: async (opts) => {
        return toWireUser(opts.ctx.user, opts.ctx.auth.isSuperUser(opts.ctx.user));
      }
    }),

    sessions: authorizedProcedure.get({
      handler: async ({ ctx, input }) => {
        const sessions = await ctx.services.db.userSessions.listSessions(ctx.user.user_id);
        return sessions.map(session => toWireUserSession(session));
      }
    }),

    currentSession: authorizedProcedure.get({
      handler: async ({ ctx, input }) => {
        return toWireUserSession(ctx.userSession);
      }
    }),

    logoutSession: authorizedProcedure.post({
      input: z.object({
        sessionId,
      }),
      handler: async ({ ctx, input }) => {
        const session = await ctx.services.db.userSessions.getByPublicId(input.sessionId);
        if (!session)
          throw TypedHTTP.HttpError.withCode('NOT_FOUND');
        if (session.user_id !== ctx.user.user_id)
          throw TypedHTTP.HttpError.withCode('UNAUTHORIZED');
        await ctx.services.db.userSessions.dropSession(session.session_id);
      }
    }),

    /**
     * Any authorized user can search for other users; why not?!
     */
    findUsers: authorizedProcedure.get({
      input: z.object({
        query: z.string().min(1),
      }),
      handler: async ({ input, ctx }) => {
        const dbUsers = await ctx.services.db.users.findUsers(input.query);      
        return dbUsers.map(user => toWireUser(user, ctx.auth.isSuperUser(user)));
      },
    }),

    /**
     * Authorized users can get the list of their organizations and projects
     */
    dashboard: authorizedProcedure.get({
      handler: async ({ ctx }) => {
        const user = ctx.user;

        // get owned orgs
        const ownedOrgIds = await ctx.services.db.users.getOrganizations(user.user_id);
        const ownedOrgs = new Map<OrgId, WireTypes.Organization>(await Promise.all(ownedOrgIds.map(async orgId => {
          const org = await ctx.services.db.orgs.get(orgId);
          assert(org);
          const billing = await ctx.services.stripeApp?.status(org);
          return [orgId, toWireOrg(user, org, Database.orgAccessRole.admin, billing)] as const;
        })));

        // get orgs the user is a member of.
        const memberOrgIds = await ctx.services.db.orgSharing.getOrganizations(user.user_id);
        const memberOrgs = new Map<OrgId, WireTypes.Organization>(await Promise.all(memberOrgIds.map(async ({ orgId, accessRole }) => {
          const org = await ctx.services.db.orgs.get(orgId);
          assert(org);
          const billing = await ctx.services.stripeApp?.status(org);
          return [orgId, toWireOrg(user, org, accessRole, billing)] as const;
        })));

        const projectIds = await ctx.services.db.projectSharing.getProjects(user.user_id);
        const projects = await Promise.all(projectIds.map(async ({ projectId, accessRole }) => {
          const project = await ctx.services.db.projects.get(projectId);
          assert(project);
          let wireOrg = ownedOrgs.get(project.org_id) ?? memberOrgs.get(project.org_id);
          if (!wireOrg) {
            const org = await ctx.services.db.orgs.get(project.org_id);
            assert(org);
            const billing = await ctx.services.stripeApp?.status(org);
            wireOrg = toWireOrg(user, org, undefined, billing);
          }
          return toWireProject(wireOrg, project, accessRole);
        }));

        return {
          ownedOrgs: [...ownedOrgs.values()],
          sharedOrgs: [...memberOrgs.values()],
          externalProjects: projects,
        }
      }
    }),

    /**
     * Organizations themselves are public, so anybody can fetch the organization
     * by name and get its ID.
     * 
     * However:
     * - organization members are visible only to org members
     * - organization projects are visible if they are configured as "public"
     */
    findOrganization: publicProcedure.get({
      input: z.object({
        orgSlug: slug,
      }),
      handler: async ({ input, ctx }) => {
        if (!ctx.org)
          return undefined;
        // Hide org limitations if the user does not have any role in the organization.
        return toWireOrg(ctx.user, ctx.org, ctx.orgRole, ctx.orgRole ? ctx.orgBillingStatus : undefined);
      },
    }),

    /**
     * Only authorized users can create new organizations.
     */
    createOrganization: authorizedProcedure.get({
      input: z.object({
        orgName: z.string().min(1).max(255),
        orgSlug: slug,
      }),
      handler: async ({ input, ctx }) => {
        if (ctx.preferences.onlySuperusersCanCreateOrganizations && !ctx.auth.isSuperUser(ctx.user))
          throw TypedHTTP.HttpError.withCode('UNAUTHORIZED');

        await ctx.services.db.orgs.create({
          org_name: input.orgName,
          org_slug: input.orgSlug,
          org_public_id: randomUUID() as OrgPublicId,
          owner_id: ctx.user.user_id,
        });
      },
    })
  },
};

// export type definition of API
export type AppRouter = typeof appRouter;
