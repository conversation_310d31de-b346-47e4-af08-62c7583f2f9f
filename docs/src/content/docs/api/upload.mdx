---
title: Commits History
sidebar:
  order: 1
---

Each [Flakiness Report](/docs/concepts/flakiness-report) has a mandatory `commitId` field, and Flakiness.io uses
this information to group runs from the same commit together:

import ThemeImage from '../../../components/ThemeImage.astro';

import CommitsChaosDark from '../../../assets/commits-chaos-dark.svg';
import CommitsChaosLight from '../../../assets/commits-chaos-light.svg';

<ThemeImage 
  light={CommitsChaosLight}
  dark={CommitsChaosDark}
  alt="Commits with runs"
/>

The **inter-relationship** between commits is fetched independently from Git Repository.
This way Flakiness.io is able to build commit history and arrange runs chronologically for any new branch or tag that
was added after the test reports were submitted.


import CommitsAlignedDark from '../../../assets/commits-aligned-dark.svg';
import CommitsAlignedLight from '../../../assets/commits-aligned-light.svg';

<ThemeImage 
  light={CommitsAlignedLight}
  dark={CommitsAlignedDark}
  alt="Commits aligned according to history"
/>

Thanks to the Git History, in the example above Flakiness.io knows that:
- `run 6` is the **latest run** on the `main` branch
- `run 6` tests both `commit D` and `commit C`
